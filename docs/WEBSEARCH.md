# Web Search Functionality in Crawl4AI

## Overview
The Crawl4AI Agent includes a powerful web search tool that allows you to search the web and retrieve results using DuckDuckGo. The tool supports advanced options, including language and region specification, safe search filtering, and time limits.

## Usage Examples

### Basic Search
```
search for python web scraping
```

### Search in a Specific Language
```
search for KI Forschung --lang de
```
This will search in German (region: `de-de`).

### Search in a Specific Region
```
search for Data Science --region fr-fr
```
This will search in French (region: `fr-fr`).

### Combine with Other Flags
```
search for AI trends --lang es --safesearch off --timelimit m
```
- `--lang es`: Search in Spanish (region: `es-es`)
- `--safesearch off`: Disable safe search
- `--timelimit m`: Restrict results to the past month

## Supported Flags
- `--lang XX` : Specify language (auto-mapped to region, e.g., `de` → `de-de`)
- `--region XX-XX` : Specify region directly (overrides `--lang`)
- `--safesearch on|moderate|off` : Control safe search filtering
- `--timelimit d|w|m|y` : Restrict results to day, week, month, or year

## Implementation Notes
- The parser extracts `--lang` and `--region` flags and ensures the correct region is passed to the web search backend.
- If neither flag is specified, the default region is `us-en` (English, United States).
- All web search parameters are covered by automated tests in `tests/agent/test_langgraph_nodes.py`.

## Extending
To add support for more languages or regions, update the `lang_region_map` in `agent/langgraph_nodes.py`.

---

For further details, see the implementation in `agent/langgraph_nodes.py` and `agent/langgraph_tools.py`.
