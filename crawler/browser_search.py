"""
Browser-based web search utilities for Bing and Google using Crawl4AI's AsyncWebCrawler for browser automation (no official API required).
"""
import asyncio
from typing import List, Dict
from crawl4ai import AsyncWebCrawler, BrowserConfig

async def crawl4ai_bing_search(query: str, max_results: int = 10, lang: str = "de") -> List[Dict]:
    """
    Perform a Bing search using browser automation via Crawl4AI and extract organic results.
    """
    search_url = f"https://www.bing.com/search?q={query}&setlang={lang}"
    async with AsyncWebCrawler(config=BrowserConfig(headless=True)) as crawler:
        result = await crawler.arun(url=search_url)
        html = result.html
        # Simple extraction: titles, links, snippets
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, "html.parser")
        items = soup.select("li.b_algo")
        results = []
        for item in items[:max_results]:
            title_tag = item.select_one("h2")
            link_tag = item.select_one("h2 a")
            snippet_tag = item.select_one(".b_caption p")
            title = title_tag.text.strip() if title_tag else ""
            url = link_tag['href'].strip() if link_tag else ""
            snippet = snippet_tag.text.strip() if snippet_tag else ""
            results.append({"title": title, "url": url, "snippet": snippet})
        return results

async def crawl4ai_google_search(query: str, max_results: int = 10, lang: str = "de") -> List[Dict]:
    """
    Perform a Google search using browser automation via Crawl4AI and extract organic results.
    """
    search_url = f"https://www.google.com/search?q={query}&hl={lang}"
    async with AsyncWebCrawler(config=BrowserConfig(headless=True)) as crawler:
        result = await crawler.arun(url=search_url)
        html = result.html

        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, "html.parser")
        items = soup.select("div.MjjYud")
        results = []
        for item in items[:max_results]:
            title_tag = item.select_one("h3.LC20lb")
            link_tag = item.select_one("a")
            snippet_tag = item.select_one("div.VwiC3b")

            title = title_tag.text.strip() if title_tag else ""
            url = link_tag['href'].strip() if link_tag and link_tag.has_attr('href') else ""
            snippet = snippet_tag.text.strip() if snippet_tag else ""
            if title and url:
                results.append({"title": title, "url": url, "snippet": snippet})
        return results

# Example usage:
# asyncio.run(crawl4ai_bing_search("Bürodrehstuhl online kaufen", lang="de"))
# results = asyncio.run(crawl4ai_bing_search(query="Crawl4AI", lang="de"))
results = asyncio.run(crawl4ai_google_search(query="Bürodrehstuhl online kaufen", lang="de"))
import json
print(json.dumps(results, indent=2, ensure_ascii=False))
