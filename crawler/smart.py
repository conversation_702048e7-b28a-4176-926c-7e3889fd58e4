"""
Smart crawling functionality with schema learning capabilities.

This module contains advanced crawling functions that use LLMs to:
- Learn CSS selectors automatically for structured data extraction
- Cache learned schemas for reuse
- Provide intelligent data extraction based on Pydantic models
"""

import os
import json
from typing import Dict, List, Optional, Any, Type
from pydantic import BaseModel
from crawl4ai import AsyncWebCrawler, CacheMode, BrowserConfig, CrawlerRunConfig, LLMConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy, JsonCssExtractionStrategy

from utils.logger import logger
from .statistics import track_crawl, CrawlType
from .core import _get_aggressive_cookie_dismissal_js


class SchemaLearner(BaseModel):
    """Schema learning and caching system for web crawling."""
    schema_dir: str = "./learned_schemas"

    def __init__(self, **data):
        super().__init__(**data)
        os.makedirs(self.schema_dir, exist_ok=True)

    def get_schema_path(self, url: str) -> str:
        """Generate a filename for the schema based on the URL."""
        domain = url.replace(
            "https://", "").replace("http://", "").split("/")[0]
        return os.path.join(self.schema_dir, f"{domain}_schema.json")

    def load_schema(self, url: str) -> Optional[Dict]:
        """Load a previously learned schema for a URL if it exists."""
        schema_path = self.get_schema_path(url)
        if os.path.exists(schema_path):
            with open(schema_path, 'r') as f:
                return json.load(f)
        return None

    def save_schema(self, url: str, schema: Dict) -> None:
        """Save a learned schema for future use."""
        schema_path = self.get_schema_path(url)
        with open(schema_path, 'w') as f:
            json.dump(schema, f, indent=2)
        logger.info(f"Schema saved to {schema_path}")


@track_crawl(crawl_type=CrawlType.SMART)
async def smart_crawler(
    url: str,
    target_model: Type[BaseModel],
    provider: str = "ollama/qwen3",
    api_token: Optional[str] = None,
    force_learn: bool = False,
    schema_dir: str = "./learned_schemas"
) -> List[Dict[str, Any]]:
    """
    Crawl a webpage, learning the schema if needed and using cached schema for future crawls.

    Args:
        url: The URL to crawl
        target_model: Pydantic model defining the data structure to extract
        provider: LLM provider to use for schema learning
        api_token: API token for the LLM provider
        force_learn: Force learning a new schema even if one exists
        schema_dir: Directory to store learned schemas

    Returns:
        Extracted data according to the target model
    """
    schema_learner = SchemaLearner(schema_dir=schema_dir)
    browser_config = BrowserConfig(headless=True)

    # Try to load existing schema
    schema = None if force_learn else schema_learner.load_schema(url)

    if schema is None:
        logger.info(f"No schema found for {url}. Learning schema with LLM...")
        # First pass: Learn the schema using LLM
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            magic=True,
            simulate_user=True,
            remove_overlay_elements=True,  # Remove popups and overlays automatically
            # Aggressive JavaScript to remove cookie consent banners
            js_code=_get_aggressive_cookie_dismissal_js(),
            extraction_strategy=LLMExtractionStrategy(
                llm_config=LLMConfig(provider=provider, api_token=api_token),
                schema=target_model.model_json_schema(),
                extraction_type="schema",
                instruction=f"""
                Analyze this webpage and create a CSS selector schema to extract data matching this structure:
                {target_model.model_json_schema()}

                Return a JSON schema with these exact fields:
                - name: A descriptive name for this extractor
                - baseSelector: CSS selector for repeated items to extract (e.g., ".product", ".article")
                - fields: Array of field definitions with:
                  - name: Field name matching the target schema properties
                  - selector: CSS selector to extract this data relative to baseSelector
                  - type: "text" for text content, "attribute" for HTML attributes
                  - attribute: Name of attribute to extract (only if type is "attribute")

                Example format:
                {{
                  "name": "Product Extractor",
                  "baseSelector": ".product-item",
                  "fields": [
                    {{"name": "name", "selector": ".product-name", "type": "text"}},
                    {{"name": "price", "selector": ".price", "type": "text"}},
                    {{"name": "image", "selector": "img", "type": "attribute", "attribute": "src"}}
                  ]
                }}
                """,
                extra_args={"temperature": 0, "max_tokens": 2000}
            )
        )

        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(url=url, config=crawler_config)
            if not result.success:
                raise Exception(
                    f"Failed to learn schema: {result.error_message}")

            # Parse the learned schema
            try:
                schema = json.loads(result.extracted_content)
                schema_learner.save_schema(url, schema)
            except Exception as e:
                raise Exception(f"Failed to parse learned schema: {e}")

    # Second pass: Use the learned schema for extraction
    logger.info(
        f"Using {'learned' if force_learn else 'cached'} schema for extraction")
    crawler_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        magic=True,
        simulate_user=True,
        remove_overlay_elements=True,  # Remove popups and overlays automatically
        # Aggressive JavaScript to remove cookie consent banners
        js_code=_get_aggressive_cookie_dismissal_js(),
        extraction_strategy=JsonCssExtractionStrategy(schema)
    )

    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(url=url, config=crawler_config)
        if not result.success:
            raise Exception(f"Failed to extract data: {result.error_message}")

        return json.loads(result.extracted_content)
