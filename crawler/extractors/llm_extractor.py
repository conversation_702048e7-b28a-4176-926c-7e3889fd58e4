"""
LLM-based company data extractor.

This module provides robust, language-aware extraction of company data
using Large Language Models as a fallback when regex extraction fails.
"""

import json
from typing import Dict, Any, Optional
from bs4 import BeautifulSoup
import html

from .base import CompanyDataExtractor, ExtractionResult
from crawler.models import CompanyData, LegalStructure, Country
from utils.logger import logger

# Simple client interface for LLM requests


class SimpleLLMClient:
    """Simple client for making LLM requests."""

    async def generate(self, model: str, prompt: str, temperature: float = 0.1, format: str = None) -> dict:
        """Generate response from LLM."""
        try:
            # Try to import and use the new LiteLLM client
            from agent.llm.client import get_chat_response_from_llm
            response_text = await get_chat_response_from_llm(prompt, model)
            return {'response': response_text}
        except ImportError:
            # Fallback for testing or when LLM client is not available
            logger.warning("LLM client not available, using mock response")
            return {'response': '{"company_name": "", "error": "LLM not available"}'}


class LLMCompanyDataExtractor(CompanyDataExtractor):
    """
    LLM-based extractor for company data from legal pages.

    Uses language models to understand context and extract structured
    company information with high accuracy and language awareness.
    """

    def __init__(self, model: str = "qwen3", temperature: float = 0.1):
        """
        Initialize the LLM extractor.

        Args:
            model: LLM model to use for extraction
            temperature: Temperature for LLM generation (lower = more deterministic)
        """
        super().__init__("llm")
        self.model = model
        self.temperature = temperature
        self.llm_client = SimpleLLMClient()

    async def extract(self, html_content: str, source_url: str = "") -> ExtractionResult:
        """
        Extract company data using LLM processing.

        Args:
            html_content: Raw HTML content to extract from
            source_url: Source URL for metadata

        Returns:
            ExtractionResult with extracted company data
        """
        start_time = self._start_timing()

        try:
            # Clean and prepare content
            text_content = self._prepare_content(html_content)

            if not text_content.strip():
                return self._create_result(
                    data={},
                    success=False,
                    confidence_score=0.0,
                    processing_time_ms=self._end_timing(start_time),
                    source_url=source_url,
                    error_message="No text content found",
                    raw_content_length=len(html_content),
                    processed_content_length=0
                )

            # Extract data using LLM
            extracted_data = await self._extract_with_llm(text_content, source_url)

            # Validate and clean extracted data
            validated_data = self._validate_extracted_data(extracted_data)

            # Calculate confidence score
            confidence = self._calculate_confidence(
                validated_data, text_content)

            # Determine success
            success = confidence > 0.2  # Higher threshold for LLM extraction

            return self._create_result(
                data=validated_data,
                success=success,
                confidence_score=confidence,
                processing_time_ms=self._end_timing(start_time),
                source_url=source_url,
                raw_content_length=len(html_content),
                processed_content_length=len(text_content)
            )

        except Exception as e:
            logger.error(f"LLM extraction failed: {e}")
            return self._create_result(
                data={},
                success=False,
                confidence_score=0.0,
                processing_time_ms=self._end_timing(start_time),
                source_url=source_url,
                error_message=str(e),
                raw_content_length=len(html_content)
            )

    def _prepare_content(self, html_content: str) -> str:
        """
        Clean and prepare HTML content for LLM processing.

        Args:
            html_content: Raw HTML content

        Returns:
            Cleaned text content optimized for LLM processing
        """
        try:
            # Parse HTML and extract text
            soup = BeautifulSoup(html_content, 'html.parser')

            # Remove unwanted elements
            for element in soup(["script", "style", "nav", "header", "footer", "aside"]):
                element.decompose()

            # Get text content
            text = soup.get_text()

            # Clean up whitespace and decode HTML entities
            text = html.unescape(text)
            text = '\n'.join(line.strip()
                             for line in text.split('\n') if line.strip())

            # Limit content length for LLM processing (keep most relevant parts)
            if len(text) > 4000:
                # Try to find impressum-related content
                lines = text.split('\n')
                relevant_lines = []

                for line in lines:
                    line_lower = line.lower()
                    if any(keyword in line_lower for keyword in [
                        'impressum', 'imprint', 'legal', 'geschäftsführer', 'managing director',
                        'handelsregister', 'commercial register', 'umsatzsteuer', 'vat',
                        'telefon', 'phone', 'email', 'adresse', 'address'
                    ]):
                        relevant_lines.append(line)
                    elif len(relevant_lines) > 0 and len(relevant_lines) < 50:
                        # Include some context around relevant lines
                        relevant_lines.append(line)

                if relevant_lines:
                    text = '\n'.join(relevant_lines)
                else:
                    # Fallback: take first 4000 characters
                    text = text[:4000]

            return text.strip()

        except Exception as e:
            logger.warning(f"HTML parsing failed, using raw content: {e}")
            # Fallback to raw content cleaning
            text = html.unescape(html_content)
            text = '\n'.join(line.strip()
                             for line in text.split('\n') if line.strip())
            return text[:4000] if len(text) > 4000 else text

    async def _extract_with_llm(self, text_content: str, source_url: str) -> Dict[str, Any]:
        """
        Extract company data using LLM.

        Args:
            text_content: Cleaned text content
            source_url: Source URL for context

        Returns:
            Dictionary with extracted company data
        """
        # Build extraction prompt
        prompt = self._build_extraction_prompt(text_content, source_url)

        try:
            # Make LLM request
            response = await self.llm_client.generate(
                model=self.model,
                prompt=prompt,
                temperature=self.temperature,
                format="json"
            )

            if not response or not response.get('response'):
                raise Exception("Empty response from LLM")

            # Parse JSON response
            response_text = response['response'].strip()

            # Try to extract JSON from response
            try:
                extracted_data = json.loads(response_text)
            except json.JSONDecodeError:
                # Try to find JSON in response text
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    extracted_data = json.loads(json_text)
                else:
                    raise Exception("Could not parse JSON from LLM response")

            return extracted_data

        except Exception as e:
            logger.error(f"LLM extraction request failed: {e}")
            raise Exception(f"LLM extraction failed: {str(e)}")

    def _build_extraction_prompt(self, text_content: str, source_url: str) -> str:
        """
        Build the extraction prompt for the LLM.

        Args:
            text_content: Text content to extract from
            source_url: Source URL for context

        Returns:
            Formatted prompt string
        """
        return f"""Extract structured company data from the following legal notice/impressum text. 
Return ONLY a valid JSON object with the specified fields. If a field cannot be found, use an empty string.

REQUIRED JSON STRUCTURE:
{{
    "company_name": "",
    "legal_structure": "",
    "street_address": "",
    "zip_code": "",
    "city": "",
    "state_province": "",
    "country": "",
    "phone": "",
    "fax": "",
    "email": "",
    "website": "",
    "managing_director": "",
    "management_title": "",
    "registration_number": "",
    "register_court": "",
    "tax_id": "",
    "vat_id": ""
}}

EXTRACTION GUIDELINES:
- Extract company name including legal structure (GmbH, AG, LLC, etc.)
- For legal_structure, use one of: GmbH, AG, e.K., KG, OHG, UG, GbR, LLC, Inc, Ltd, Corp, PLC, SA, SRL, B.V., AB, Other, Unknown
- For country, use full country names: Germany, Austria, Switzerland, Netherlands, etc.
- Clean phone numbers but keep original formatting
- Validate email addresses
- For German companies, look for: Geschäftsführer, Handelsregister, Registergericht, Steuernummer, USt-IdNr.
- For managing_director, extract the person's name, not just the title
- For management_title, use terms like: Geschäftsführer, CEO, Managing Director, Inhaber, etc.

TEXT TO EXTRACT FROM:
{text_content}

Return only the JSON object, no additional text or explanation."""

    def _validate_extracted_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean extracted data.

        Args:
            data: Raw extracted data from LLM

        Returns:
            Validated and cleaned data dictionary
        """
        if not isinstance(data, dict):
            return {}

        # Define expected fields with defaults
        validated_data = {
            "company_name": "",
            "legal_structure": "Unknown",
            "street_address": "",
            "zip_code": "",
            "city": "",
            "state_province": "",
            "country": "Unknown",
            "phone": "",
            "fax": "",
            "email": "",
            "website": "",
            "managing_director": "",
            "management_title": "",
            "registration_number": "",
            "register_court": "",
            "tax_id": "",
            "vat_id": ""
        }

        # Copy and validate each field
        for field, default_value in validated_data.items():
            if field in data and data[field]:
                value = str(data[field]).strip()
                if value and value.lower() not in ['null', 'none', 'n/a', 'not available']:
                    validated_data[field] = value
                else:
                    validated_data[field] = default_value
            else:
                validated_data[field] = default_value

        # Validate specific field formats
        validated_data = self._validate_field_formats(validated_data)

        return validated_data

    def _validate_field_formats(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean specific field formats.

        Args:
            data: Data dictionary to validate

        Returns:
            Data dictionary with validated field formats
        """
        # Validate email
        if data.get("email"):
            email = data["email"].strip()
            if "@" not in email or "." not in email.split("@")[-1]:
                data["email"] = ""

        # Validate website
        if data.get("website"):
            website = data["website"].strip()
            if not website.startswith(("http://", "https://")):
                if "." in website:
                    data["website"] = "https://" + website
                else:
                    data["website"] = ""

        # Clean phone numbers
        if data.get("phone"):
            phone = data["phone"].strip()
            # Keep only digits, +, -, (, ), and spaces
            import re
            cleaned_phone = re.sub(r'[^\d+\-\(\)\s]', '', phone)
            data["phone"] = cleaned_phone

        if data.get("fax"):
            fax = data["fax"].strip()
            import re
            cleaned_fax = re.sub(r'[^\d+\-\(\)\s]', '', fax)
            data["fax"] = cleaned_fax

        # Validate legal structure
        if data.get("legal_structure"):
            structure = data["legal_structure"].upper().strip()
            valid_structures = [
                "GMBH", "AG", "E.K.", "KG", "OHG", "UG", "GBR",
                "LLC", "INC", "LTD", "CORP", "PLC", "SA", "SRL", "B.V.", "AB"
            ]
            if structure not in valid_structures:
                if any(valid in structure for valid in valid_structures):
                    # Try to extract valid structure
                    for valid in valid_structures:
                        if valid in structure:
                            data["legal_structure"] = valid
                            break
                else:
                    data["legal_structure"] = "Other" if structure else "Unknown"

        # Validate country
        if data.get("country"):
            country = data["country"].strip()
            country_mapping = {
                "deutschland": "Germany",
                "germany": "Germany",
                "österreich": "Austria",
                "austria": "Austria",
                "schweiz": "Switzerland",
                "switzerland": "Switzerland",
                "niederlande": "Netherlands",
                "netherlands": "Netherlands",
                "belgien": "Belgium",
                "belgium": "Belgium",
                "frankreich": "France",
                "france": "France",
                "italien": "Italy",
                "italy": "Italy",
                "spanien": "Spain",
                "spain": "Spain",
                "vereinigtes königreich": "United Kingdom",
                "united kingdom": "United Kingdom",
                "uk": "United Kingdom",
                "usa": "United States",
                "united states": "United States",
                "kanada": "Canada",
                "canada": "Canada"
            }
            normalized_country = country_mapping.get(country.lower(), country)
            data["country"] = normalized_country

        return data

    def _calculate_confidence(self, data: Dict[str, Any], text_content: str) -> float:
        """
        Calculate confidence score for LLM extraction.

        Args:
            data: Extracted data dictionary
            text_content: Original text content

        Returns:
            Confidence score between 0.0 and 1.0
        """
        score = 0.0
        max_score = 0.0

        # Weight different fields by importance
        field_weights = {
            "company_name": 0.30,
            "street_address": 0.15,
            "city": 0.10,
            "email": 0.15,
            "phone": 0.10,
            "managing_director": 0.10,
            "registration_number": 0.05,
            "vat_id": 0.05
        }

        for field, weight in field_weights.items():
            max_score += weight
            if field in data and data[field] and str(data[field]).strip():
                value = str(data[field]).strip()
                if value not in ["Unknown", "Other", ""]:
                    score += weight

        # Bonus for having legal structure
        if data.get("legal_structure") and data["legal_structure"] not in ["Unknown", "Other", ""]:
            score += 0.05
            max_score += 0.05

        # Bonus for data consistency (check if extracted data appears in original text)
        consistency_bonus = 0.0
        text_lower = text_content.lower()

        if data.get("company_name"):
            company_name = data["company_name"].lower()
            if len(company_name) > 3 and company_name in text_lower:
                consistency_bonus += 0.05

        if data.get("email"):
            email = data["email"].lower()
            if email in text_lower:
                consistency_bonus += 0.05

        if data.get("city"):
            city = data["city"].lower()
            if len(city) > 2 and city in text_lower:
                consistency_bonus += 0.03

        score += consistency_bonus
        max_score += 0.13  # Maximum possible consistency bonus

        return min(score / max_score if max_score > 0 else 0.0, 1.0)
