#!/usr/bin/env python3
"""
Easy provider switching utility for LiteLLM multi-provider setup.

Usage:
    python switch_provider.py                    # List available providers
    python switch_provider.py ollama_local       # Switch to ollama_local profile
    python switch_provider.py openai_fast        # Switch to openai_fast profile
    python switch_provider.py groq_speed         # Switch to groq_speed profile
"""

import sys
import configparser
from agent.llm.enhanced_config import EnhancedLLMConfig, list_available_profiles


def switch_provider(profile_name: str, config_file: str = 'config.ini'):
    """Switch to a specific provider profile."""
    # Load current config to validate profile exists
    enhanced_config = EnhancedLLMConfig.create_from_ini(config_file)
    
    if profile_name not in enhanced_config.profiles:
        print(f"❌ Profile '{profile_name}' not found!")
        print("\n📋 Available profiles:")
        for profile in enhanced_config.get_available_profiles():
            print(f"   • {profile.name} ({profile.provider}/{profile.model})")
        return False
    
    # Update config file
    config = configparser.ConfigParser()
    config.read(config_file)
    
    if not config.has_section('litellm'):
        config.add_section('litellm')
    
    config.set('litellm', 'active_provider', profile_name)
    
    with open(config_file, 'w') as f:
        config.write(f)
    
    # Show success message
    profile = enhanced_config.profiles[profile_name]
    print(f"✅ Switched to: {profile.name}")
    print(f"   Provider: {profile.provider}")
    print(f"   Model: {profile.model}")
    if profile.description:
        print(f"   Description: {profile.description}")
    
    return True


def main():
    """Main function for command-line usage."""
    if len(sys.argv) == 1:
        # No arguments - list available providers
        print("🔄 LiteLLM Provider Switcher")
        print("=" * 40)
        list_available_profiles()
        print("\n💡 Usage: python switch_provider.py <profile_name>")
        
    elif len(sys.argv) == 2:
        # Switch to specified provider
        profile_name = sys.argv[1]
        
        if profile_name in ['--help', '-h', 'help']:
            print(__doc__)
            return
        
        success = switch_provider(profile_name)
        if success:
            print(f"\n🚀 Ready to use {profile_name}!")
            print("   You can now run your application with the new provider.")
        
    else:
        print("❌ Too many arguments!")
        print("Usage: python switch_provider.py [profile_name]")
        sys.exit(1)


if __name__ == "__main__":
    main()
