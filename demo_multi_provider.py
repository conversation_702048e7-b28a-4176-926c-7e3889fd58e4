#!/usr/bin/env python3
"""
Demo script showing multi-provider LLM functionality.
This demonstrates how to switch between Ollama, OpenAI, and Groq providers.
"""

import asyncio
import configparser
from agent.llm.client import get_chat_response_from_llm, get_summary_from_llm


def update_config(provider, model):
    """Update the config.ini file with new provider settings."""
    config = configparser.ConfigParser()
    config.read('config.ini')
    
    config['litellm']['provider'] = provider
    config['litellm']['primary_model'] = model
    
    with open('config.ini', 'w') as configfile:
        config.write(configfile)
    
    print(f"🔄 Switched to {provider.upper()} with model: {model}")


async def test_provider_functionality(provider_name):
    """Test basic functionality with current provider."""
    print(f"\n🧪 Testing {provider_name.upper()} functionality...")
    print("-" * 50)
    
    try:
        # Test 1: Simple chat
        print("1️⃣ Testing chat response...")
        chat_response = await get_chat_response_from_llm(
            f"Hello! Please respond with: '{provider_name.upper()} is working perfectly!'"
        )
        print(f"   Response: {chat_response}")
        
        # Test 2: Summarization
        print("\n2️⃣ Testing summarization...")
        summary = await get_summary_from_llm(
            "Artificial Intelligence (AI) is revolutionizing industries worldwide. "
            "Machine learning algorithms can now process vast amounts of data, "
            "recognize patterns, and make predictions with remarkable accuracy. "
            "From healthcare to finance, AI applications are transforming how we work and live."
        )
        print(f"   Summary: {summary}")
        
        print(f"✅ {provider_name.upper()} tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ {provider_name.upper()} test failed: {str(e)}")
        return False


async def demo_provider_switching():
    """Demonstrate switching between different LLM providers."""
    print("🚀 Multi-Provider LLM Demo")
    print("=" * 60)
    print("This demo shows how to switch between Ollama, OpenAI, and Groq providers")
    print("=" * 60)
    
    # Provider configurations
    providers = [
        ("ollama", "qwen3"),
        ("openai", "gpt-4o-mini"),
        ("groq", "llama-3.1-8b-instant")
    ]
    
    results = {}
    
    for provider, model in providers:
        print(f"\n{'='*20} {provider.upper()} DEMO {'='*20}")
        
        # Update configuration
        update_config(provider, model)
        
        # Test functionality
        success = await test_provider_functionality(provider)
        results[provider] = success
        
        if success:
            print(f"🎉 {provider.upper()} is working correctly!")
        else:
            print(f"⚠️  {provider.upper()} encountered issues")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEMO SUMMARY")
    print("=" * 60)
    
    working_providers = []
    for provider, success in results.items():
        status = "✅ WORKING" if success else "❌ FAILED"
        print(f"{provider.upper():10} | {status}")
        if success:
            working_providers.append(provider)
    
    print(f"\n🎯 Result: {len(working_providers)}/3 providers are working")
    
    if working_providers:
        print(f"✅ Working providers: {', '.join(working_providers)}")
        print("\n💡 Tips:")
        print("   • You can switch providers by updating 'provider' in config.ini")
        print("   • Each provider has different strengths:")
        print("     - Ollama: Local, private, no API costs")
        print("     - OpenAI: High quality, reliable, good for production")
        print("     - Groq: Very fast inference, good for real-time applications")
    else:
        print("❌ No providers are working. Please check your configuration.")
    
    return results


async def demo_real_world_usage():
    """Demonstrate real-world usage scenarios."""
    print("\n" + "=" * 60)
    print("🌍 REAL-WORLD USAGE SCENARIOS")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "Fast Response (Groq)",
            "provider": "groq",
            "model": "llama-3.1-8b-instant",
            "task": "Quick question answering",
            "prompt": "What is the capital of France? (one word answer)"
        },
        {
            "name": "High Quality (OpenAI)",
            "provider": "openai", 
            "model": "gpt-4o-mini",
            "task": "Complex analysis",
            "prompt": "Analyze the pros and cons of renewable energy in 2-3 sentences."
        },
        {
            "name": "Private/Local (Ollama)",
            "provider": "ollama",
            "model": "qwen3",
            "task": "Sensitive data processing",
            "prompt": "Summarize this confidential data: Company revenue increased 15% this quarter."
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🎯 Scenario: {scenario['name']}")
        print(f"   Use case: {scenario['task']}")
        
        # Switch to appropriate provider
        update_config(scenario['provider'], scenario['model'])
        
        try:
            response = await get_chat_response_from_llm(scenario['prompt'])
            print(f"   Response: {response}")
            print(f"   ✅ Success with {scenario['provider'].upper()}")
        except Exception as e:
            print(f"   ❌ Failed with {scenario['provider'].upper()}: {str(e)}")


if __name__ == "__main__":
    print("🎭 LiteLLM Multi-Provider Demo")
    print("This script demonstrates the multi-provider LLM capabilities")
    print()
    
    # Run the demo
    asyncio.run(demo_provider_switching())
    
    # Show real-world scenarios
    asyncio.run(demo_real_world_usage())
    
    print("\n" + "=" * 60)
    print("🏁 Demo completed!")
    print("You can now use any of the working providers in your application.")
    print("Simply update the 'provider' setting in config.ini to switch between them.")
    print("=" * 60)
