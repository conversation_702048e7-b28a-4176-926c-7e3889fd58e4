# Overlay Removal Tests

This directory contains tests for the aggressive cookie consent banner removal functionality implemented in the crawler system.

## Test Files

### `test_aggressive_removal_verification.py`
**Purpose**: Comprehensive verification test for aggressive cookie banner removal

**What it tests**:
- Compares baseline crawling (without aggressive removal) vs. aggressive removal
- Measures content reduction and cookie mention reduction
- Tests on multiple websites (assmann.de, girsberger.de)
- Provides detailed improvement analysis

**Usage**:
```bash
cd /path/to/project
python tests/overlay_removal/test_aggressive_removal_verification.py
```

**Expected Results**:
- Content length reduction indicating successful banner removal
- Fewer cookie-related mentions in extracted content
- Screenshots saved for visual verification
- Console output showing improvement metrics

## Features Tested

### Aggressive Cookie Banner Removal
- **Multi-step JavaScript removal**: Click dismiss buttons → Remove CCM19 elements → Remove generic cookie elements → Remove modal overlays
- **Comprehensive targeting**: CCM19, OneTrust, CookieConsent, and generic cookie banner systems
- **Extended processing time**: 2-second delay for JavaScript execution
- **Visual verification**: Screenshot capture to verify removal

### Supported Cookie Consent Systems
- **CCM19**: Used by assmann.de and similar German websites
- **OneTrust**: Popular enterprise cookie consent platform
- **CookieConsent**: Generic cookie consent library
- **Generic systems**: Custom implementations using common patterns

## Test Results Reference

### assmann.de
- Content removed: ~1,652 characters (3.4% reduction)
- Cookie mentions reduced: 50% (from 4 to 2)
- Processing time: 1.22s → 6.47s

### girsberger.de
- Content removed: ~299 characters (1.2% reduction)
- Cookie mentions: Maintained at low level
- Processing time: 1.45s → 5.89s

## Running Tests

### Prerequisites
- Virtual environment activated
- Playwright browsers installed (`playwright install`)
- All project dependencies installed

### Individual Test Execution
```bash
# From project root
python tests/overlay_removal/test_aggressive_removal_verification.py
```

### Integration with Test Suite
These tests can be integrated into the main test suite by importing them in the appropriate test modules.

## Visual Verification

Tests generate screenshots in the `screenshots/` directory. Check these files to visually verify that cookie consent banners have been removed:
- `screenshots/assmann.de/` - CCM19 cookie banner removal
- `screenshots/girsberger.de/` - Generic cookie banner removal

## Troubleshooting

### Common Issues
1. **Playwright not installed**: Run `playwright install` in your virtual environment
2. **Network timeouts**: Some websites may be slow; increase timeout values if needed
3. **Cookie banners still visible**: Check console logs for JavaScript execution errors

### Debug Information
Tests include console logging from the JavaScript removal code. Look for:
- `🍪 Starting aggressive cookie banner removal...`
- `🖱️ Clicking dismiss button: [selector]`
- `🗑️ Removing [element type]: [selector]`
- `✅ Aggressive cookie banner removal completed!`
