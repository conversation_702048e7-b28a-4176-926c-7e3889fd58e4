#!/usr/bin/env python3
"""
Test script specifically for assmann.de to verify enhanced overlay removal
including cookie consent banners.
"""

import asyncio
from crawler import crawl_website, download_html


async def test_assmann_overlay_removal():
    """Test enhanced overlay removal specifically on assmann.de."""
    
    test_url = "https://assmann.de"
    
    print("🧪 Testing enhanced overlay removal on assmann.de...")
    print("This test verifies that cookie consent banners are properly removed.")
    
    try:
        print(f"\n1. Testing crawl_website on {test_url}...")
        result = await crawl_website(test_url, screenshot=True)
        
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result
        
        if markdown_content:
            print("✅ crawl_website completed successfully")
            print(f"📄 Page title: {page_title}")
            print(f"📝 Markdown content length: {len(markdown_content)} characters")
            if html_path:
                print(f"💾 HTML saved to: {html_path}")
            if screenshot_path:
                print(f"📸 Screenshot saved to: {screenshot_path}")
                print("🔍 Check the screenshot to verify cookie overlays are removed!")
            
            # Check if content contains cookie-related text (should be minimal after removal)
            cookie_indicators = ['cookie', 'consent', 'gdpr', 'privacy policy']
            cookie_mentions = sum(1 for indicator in cookie_indicators 
                                if indicator.lower() in markdown_content.lower())
            print(f"🍪 Cookie-related mentions in content: {cookie_mentions}")
            
        else:
            print("❌ crawl_website failed - no content returned")
            
    except Exception as e:
        print(f"❌ crawl_website error: {e}")
    
    try:
        print(f"\n2. Testing download_html on {test_url}...")
        result = await download_html(test_url, screenshot=True)
        
        html_path, page_title, screenshot_path, pdf_path = result
        
        if html_path:
            print("✅ download_html completed successfully")
            print(f"📄 Page title: {page_title}")
            print(f"💾 HTML saved to: {html_path}")
            if screenshot_path:
                print(f"📸 Screenshot saved to: {screenshot_path}")
                print("🔍 Check the screenshot to verify cookie overlays are removed!")
        else:
            print("❌ download_html failed - no HTML path returned")
            
    except Exception as e:
        print(f"❌ download_html error: {e}")
    
    print("\n🎉 Enhanced overlay removal testing completed!")
    print("\n📋 What was tested:")
    print("  ✓ remove_overlay_elements=True parameter")
    print("  ✓ magic=True for automatic popup handling")
    print("  ✓ excluded_selector targeting cookie consent banners")
    print("  ✓ JavaScript code to actively dismiss cookie banners")
    print("  ✓ Screenshot capture to visually verify removal")
    
    print("\n🔧 Technical details:")
    print("  • CSS selectors target common cookie banner patterns")
    print("  • JavaScript actively clicks dismiss/accept buttons")
    print("  • Multiple fallback strategies for maximum effectiveness")
    print("  • OneTrust, CookieConsent, and other popular libraries supported")


if __name__ == "__main__":
    asyncio.run(test_assmann_overlay_removal())
