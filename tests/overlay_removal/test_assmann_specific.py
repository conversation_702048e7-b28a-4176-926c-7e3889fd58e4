#!/usr/bin/env python3
"""
Specific test for assmann.de with different configurations to find what works.
"""

import asyncio
from crawl4ai import Async<PERSON>ebCrawler, CacheMode, BrowserConfig, CrawlerRunConfig


async def test_assmann_configurations():
    """Test different configurations for assmann.de."""
    
    url = "https://assmann.de"
    
    print("🧪 Testing different configurations for assmann.de...")
    
    # Configuration 1: Minimal configuration
    print("\n1. Testing minimal configuration...")
    try:
        browser_config = BrowserConfig(headless=True)
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            screenshot=True
        )
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(url=url, config=crawler_config)
            
            if result.success:
                print(f"✅ Minimal config success - Title: {result.metadata.get('title', 'N/A')}")
                print(f"📝 Content length: {len(result.markdown)} characters")
                if result.screenshot:
                    print("📸 Screenshot captured")
            else:
                print(f"❌ Minimal config failed: {result.error_message}")
                
    except Exception as e:
        print(f"❌ Minimal config error: {e}")
    
    # Configuration 2: With magic but no overlay removal
    print("\n2. Testing with magic but no overlay removal...")
    try:
        browser_config = BrowserConfig(headless=True)
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            magic=True,
            screenshot=True
        )
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(url=url, config=crawler_config)
            
            if result.success:
                print(f"✅ Magic config success - Title: {result.metadata.get('title', 'N/A')}")
                print(f"📝 Content length: {len(result.markdown)} characters")
                if result.screenshot:
                    print("📸 Screenshot captured")
            else:
                print(f"❌ Magic config failed: {result.error_message}")
                
    except Exception as e:
        print(f"❌ Magic config error: {e}")
    
    # Configuration 3: With overlay removal but no magic
    print("\n3. Testing with overlay removal but no magic...")
    try:
        browser_config = BrowserConfig(headless=True)
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            remove_overlay_elements=True,
            screenshot=True
        )
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(url=url, config=crawler_config)
            
            if result.success:
                print(f"✅ Overlay removal config success - Title: {result.metadata.get('title', 'N/A')}")
                print(f"📝 Content length: {len(result.markdown)} characters")
                if result.screenshot:
                    print("📸 Screenshot captured")
            else:
                print(f"❌ Overlay removal config failed: {result.error_message}")
                
    except Exception as e:
        print(f"❌ Overlay removal config error: {e}")
    
    # Configuration 4: With different wait conditions
    print("\n4. Testing with different wait conditions...")
    try:
        browser_config = BrowserConfig(headless=True)
        crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            wait_until="domcontentloaded",  # Different wait condition
            screenshot=True
        )
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(url=url, config=crawler_config)
            
            if result.success:
                print(f"✅ Different wait config success - Title: {result.metadata.get('title', 'N/A')}")
                print(f"📝 Content length: {len(result.markdown)} characters")
                if result.screenshot:
                    print("📸 Screenshot captured")
            else:
                print(f"❌ Different wait config failed: {result.error_message}")
                
    except Exception as e:
        print(f"❌ Different wait config error: {e}")
    
    print("\n🎉 Configuration testing completed!")


if __name__ == "__main__":
    asyncio.run(test_assmann_configurations())
