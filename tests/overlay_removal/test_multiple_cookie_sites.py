#!/usr/bin/env python3
"""
Test script to verify overlay removal on multiple sites with cookie banners.
"""

import asyncio
from crawler import crawl_website


async def test_multiple_cookie_sites():
    """Test overlay removal on multiple sites with cookie consent banners."""
    
    # Test URLs with cookie consent banners
    test_urls = [
        "https://assmann.de",
        "https://girsberger.de",
    ]
    
    print("🧪 Testing overlay removal on multiple sites with cookie banners...")
    
    for i, test_url in enumerate(test_urls, 1):
        print(f"\n{i}. Testing {test_url}...")
        
        try:
            result = await crawl_website(test_url, screenshot=True)
            markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result
            
            if markdown_content:
                print(f"✅ Success: {test_url}")
                print(f"📄 Page title: {page_title}")
                print(f"📝 Content length: {len(markdown_content)} characters")
                if html_path:
                    print(f"💾 HTML saved to: {html_path}")
                if screenshot_path:
                    print(f"📸 Screenshot saved to: {screenshot_path}")
                    print("🔍 Please check the screenshot to verify if cookie overlays are removed!")
                
                # Check for cookie-related content in markdown
                cookie_indicators = ['cookie', 'consent', 'gdpr', 'privacy policy', 'datenschutz', 'ccm']
                cookie_mentions = sum(1 for indicator in cookie_indicators 
                                    if indicator.lower() in markdown_content.lower())
                print(f"🍪 Cookie-related mentions in content: {cookie_mentions}")
                
                # Check if cookie banner HTML is present in the content
                cookie_banner_indicators = ['ccm-modal', 'cookie-banner', 'cookie-consent', 'gdpr-banner']
                banner_found = any(indicator in markdown_content.lower() for indicator in cookie_banner_indicators)
                if banner_found:
                    print("⚠️  Cookie banner HTML still present in content")
                else:
                    print("✅ No cookie banner HTML found in content")
                
            else:
                print(f"❌ Failed: {test_url} - no content returned")
                
        except Exception as e:
            print(f"❌ Error with {test_url}: {e}")
    
    print("\n🎉 Multi-site cookie banner testing completed!")
    print("\n📋 Next steps:")
    print("1. Check the screenshots to visually verify if cookie banners are removed")
    print("2. If banners are still visible, we need to implement a more aggressive approach")


if __name__ == "__main__":
    asyncio.run(test_multiple_cookie_sites())
