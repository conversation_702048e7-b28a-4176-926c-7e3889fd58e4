#!/usr/bin/env python3
"""
Test script to verify that remove_overlay_elements parameter is working
in all relevant crawler functions.
"""

import asyncio
from crawler import (
    crawl_website,
    crawl_impressum,
    smart_crawler,
    download_html
)
from pydantic import BaseModel
from typing import List


class TestProduct(BaseModel):
    """Simple test model for smart crawler."""
    model_config = {}
    name: str
    price: str


async def test_overlay_removal():
    """Test that overlay removal is working in all crawler functions."""
    
    # Test URL that might have overlays (using a simple example)
    test_url = "https://example.com"
    
    print("🧪 Testing overlay removal in crawler functions...")
    
    try:
        # Test 1: crawl_website
        print("\n1. Testing crawl_website...")
        result = await crawl_website(test_url)
        if result[0]:  # markdown_content
            print("✅ crawl_website completed successfully")
        else:
            print("❌ crawl_website failed")
            
    except Exception as e:
        print(f"❌ crawl_website error: {e}")
    
    try:
        # Test 2: download_html
        print("\n2. Testing download_html...")
        result = await download_html(test_url)
        if result[0]:  # html_path
            print("✅ download_html completed successfully")
        else:
            print("❌ download_html failed")
            
    except Exception as e:
        print(f"❌ download_html error: {e}")
    
    try:
        # Test 3: crawl_impressum
        print("\n3. Testing crawl_impressum...")
        result = await crawl_impressum(test_url)
        if result[0] or result[1]:  # markdown_content or html_path
            print("✅ crawl_impressum completed successfully")
        else:
            print("⚠️ crawl_impressum completed but found no impressum (expected for example.com)")
            
    except Exception as e:
        print(f"❌ crawl_impressum error: {e}")
    
    try:
        # Test 4: smart_crawler (requires LLM setup, so might fail)
        print("\n4. Testing smart_crawler...")
        result = await smart_crawler(test_url, TestProduct)
        print("✅ smart_crawler completed successfully")
            
    except Exception as e:
        print(f"⚠️ smart_crawler error (expected without LLM setup): {e}")
    
    print("\n🎉 Overlay removal testing completed!")
    print("All functions now include remove_overlay_elements=True parameter")


if __name__ == "__main__":
    asyncio.run(test_overlay_removal())
