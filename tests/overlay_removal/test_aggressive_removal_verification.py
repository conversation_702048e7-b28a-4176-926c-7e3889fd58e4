#!/usr/bin/env python3
"""
Test script to verify the aggressive cookie banner removal is working
by checking console logs and comparing before/after content.
"""

import asyncio
from crawl4ai import AsyncWebCrawler, CacheMode, BrowserConfig, CrawlerRunConfig


async def test_aggressive_removal_verification():
    """Test aggressive cookie removal with detailed verification."""
    
    test_urls = [
        "https://assmann.de",
        "https://girsberger.de"
    ]
    
    print("🧪 Testing aggressive cookie banner removal with verification...")
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{'='*60}")
        print(f"{i}. Testing {url}")
        print('='*60)
        
        # Test 1: Without aggressive removal (baseline)
        print("\n📊 Baseline test (without aggressive removal)...")
        try:
            browser_config = BrowserConfig(headless=True)
            crawler_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                screenshot=True
            )
            
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)
                
                if result.success:
                    baseline_length = len(result.markdown)
                    baseline_title = result.metadata.get('title', 'N/A')
                    print(f"✅ Baseline - Title: {baseline_title}")
                    print(f"📝 Baseline content length: {baseline_length} characters")
                    
                    # Count cookie-related content
                    cookie_indicators = ['cookie', 'consent', 'gdpr', 'datenschutz', 'ccm']
                    baseline_cookies = sum(1 for indicator in cookie_indicators 
                                         if indicator.lower() in result.markdown.lower())
                    print(f"🍪 Baseline cookie mentions: {baseline_cookies}")
                else:
                    print(f"❌ Baseline test failed: {result.error_message}")
                    continue
                    
        except Exception as e:
            print(f"❌ Baseline test error: {e}")
            continue
        
        # Test 2: With aggressive removal
        print("\n🚀 Aggressive removal test...")
        try:
            from crawler import crawl_website
            result = await crawl_website(url, screenshot=True)
            markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result
            
            if markdown_content:
                aggressive_length = len(markdown_content)
                print(f"✅ Aggressive - Title: {page_title}")
                print(f"📝 Aggressive content length: {aggressive_length} characters")
                
                # Count cookie-related content
                aggressive_cookies = sum(1 for indicator in cookie_indicators 
                                       if indicator.lower() in markdown_content.lower())
                print(f"🍪 Aggressive cookie mentions: {aggressive_cookies}")
                
                # Calculate improvement
                content_removed = baseline_length - aggressive_length
                cookie_reduction = baseline_cookies - aggressive_cookies
                
                print(f"\n📈 IMPROVEMENT ANALYSIS:")
                print(f"📉 Content removed: {content_removed} characters ({content_removed/baseline_length*100:.1f}%)")
                print(f"🍪 Cookie mentions reduced: {cookie_reduction} ({cookie_reduction/baseline_cookies*100 if baseline_cookies > 0 else 0:.1f}%)")
                
                if content_removed > 0:
                    print("✅ Cookie banner content successfully removed!")
                else:
                    print("⚠️  No significant content removed - may need further optimization")
                
                if screenshot_path:
                    print(f"📸 Screenshot with aggressive removal: {screenshot_path}")
                    
            else:
                print("❌ Aggressive test failed - no content returned")
                
        except Exception as e:
            print(f"❌ Aggressive test error: {e}")
    
    print(f"\n{'='*60}")
    print("🎉 Aggressive cookie banner removal verification completed!")
    print("📋 Summary:")
    print("• Check the screenshots to visually verify cookie banner removal")
    print("• Content length reduction indicates successful banner removal")
    print("• Fewer cookie mentions suggest cleaner content extraction")
    print(f"{'='*60}")


if __name__ == "__main__":
    asyncio.run(test_aggressive_removal_verification())
