#!/usr/bin/env python3
"""
Test script to verify enhanced overlay removal on various websites
with cookie consent banners.
"""

import asyncio
from crawler import crawl_website


async def test_general_overlay_removal():
    """Test enhanced overlay removal on various websites."""
    
    # Test URLs with different types of cookie banners
    test_urls = [
        "https://example.com",  # Simple test
        "https://github.com",   # GitHub cookie banner
        "https://stackoverflow.com",  # Stack Overflow cookie banner
    ]
    
    print("🧪 Testing enhanced overlay removal on various websites...")
    
    for i, test_url in enumerate(test_urls, 1):
        print(f"\n{i}. Testing {test_url}...")
        
        try:
            result = await crawl_website(test_url, screenshot=True)
            markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result
            
            if markdown_content:
                print(f"✅ Success: {test_url}")
                print(f"📄 Page title: {page_title}")
                print(f"📝 Content length: {len(markdown_content)} characters")
                if html_path:
                    print(f"💾 HTML saved to: {html_path}")
                if screenshot_path:
                    print(f"📸 Screenshot saved to: {screenshot_path}")
                
                # Check for cookie-related content
                cookie_indicators = ['cookie', 'consent', 'gdpr', 'privacy policy']
                cookie_mentions = sum(1 for indicator in cookie_indicators 
                                    if indicator.lower() in markdown_content.lower())
                print(f"🍪 Cookie-related mentions: {cookie_mentions}")
                
            else:
                print(f"❌ Failed: {test_url} - no content returned")
                
        except Exception as e:
            print(f"❌ Error with {test_url}: {e}")
    
    print("\n🎉 General overlay removal testing completed!")


if __name__ == "__main__":
    asyncio.run(test_general_overlay_removal())
