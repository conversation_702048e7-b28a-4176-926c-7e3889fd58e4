"""
Test to verify that the migration from Ollama to LiteLLM maintains compatibility.
"""

import unittest
from unittest.mock import AsyncMock, patch, MagicMock
import warnings

class TestMigrationCompatibility(unittest.IsolatedAsyncioTestCase):
    """Test that both old and new interfaces work correctly."""

    def test_ollama_client_deprecation_warning(self):
        """Test that importing from ollama_client shows deprecation warning."""
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # This should trigger a deprecation warning
            from agent.ollama_client import get_summary_from_ollama
            
            # Check that a warning was issued
            self.assertTrue(len(w) > 0)
            self.assertTrue(any(issubclass(warning.category, DeprecationWarning) for warning in w))

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_new_llm_interface_works(self, mock_acompletion, mock_create_config):
        """Test that the new LiteLLM interface works correctly."""
        from agent.llm import get_summary_from_llm
        from agent.llm.llm_config import LLMConfig
        
        # Mock the response
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Test summary from LiteLLM"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_acompletion.return_value = mock_response
        mock_create_config.return_value = LLMConfig()

        result = await get_summary_from_llm("Test content")
        self.assertEqual(result, "Test summary from LiteLLM")
        mock_acompletion.assert_called_once()

    @patch('agent.ollama.base_client.httpx.AsyncClient')
    async def test_old_ollama_interface_still_works(self, mock_client_class):
        """Test that the old Ollama interface still works for backward compatibility."""
        from agent.ollama import get_summary_from_ollama
        
        # Mock HTTP response
        mock_response = MagicMock()
        mock_response.json.return_value = {"response": "Test summary from Ollama"}
        mock_response.raise_for_status.return_value = None

        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await get_summary_from_ollama("Test content")
        self.assertEqual(result, "Test summary from Ollama")

    def test_function_imports_work(self):
        """Test that all expected functions can be imported."""
        # Test new LiteLLM imports
        from agent.llm import (
            get_summary_from_llm,
            get_chat_response_from_llm,
            parse_command_from_prompt_llm,
            extract_company_data_from_impressum_llm
        )
        
        # Test old Ollama imports (should work but with warnings)
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            from agent.ollama import (
                get_summary_from_ollama,
                get_chat_response_from_ollama,
                parse_command_from_prompt,
                extract_company_data_from_impressum
            )
        
        # All imports should succeed
        self.assertTrue(callable(get_summary_from_llm))
        self.assertTrue(callable(get_summary_from_ollama))

    def test_config_compatibility(self):
        """Test that configuration works for both systems."""
        from agent.llm.llm_config import LLMConfig
        from agent.ollama.config import OllamaConfig
        
        # Both config systems should work
        llm_config = LLMConfig.create_from_ini()
        ollama_config = OllamaConfig.create_default()
        
        self.assertIsNotNone(llm_config)
        self.assertIsNotNone(ollama_config)
        
        # LLM config should support multiple providers
        self.assertIn(llm_config.provider, ["ollama", "openai", "groq"])

if __name__ == '__main__':
    unittest.main()
