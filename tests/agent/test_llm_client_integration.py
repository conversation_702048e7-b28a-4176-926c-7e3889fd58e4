"""
Integration tests for the new LiteLLM client.
"""

import unittest
from unittest.mock import AsyncMock, patch, MagicMock
import litellm
from agent.llm import (
    get_summary_from_llm,
    get_chat_response_from_llm,
    parse_command_from_prompt_llm,
    extract_company_data_from_impressum_llm
)
from agent.llm.llm_config import LLMConfig


class TestLLMClientIntegration(unittest.IsolatedAsyncioTestCase):

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_get_summary_from_llm(self, mock_acompletion, mock_create_config):
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Test summary"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_acompletion.return_value = mock_response
        mock_create_config.return_value = LLMConfig()

        result = await get_summary_from_llm("Test content")
        self.assertEqual(result, "Test summary")
        mock_acompletion.assert_called_once()

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_get_chat_response_from_llm(self, mock_acompletion, mock_create_config):
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Test chat response"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_acompletion.return_value = mock_response
        mock_create_config.return_value = LLMConfig()

        result = await get_chat_response_from_llm("Hello")
        self.assertEqual(result, "Test chat response")
        mock_acompletion.assert_called_once()

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_parse_command_from_prompt_llm(self, mock_acompletion, mock_create_config):
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = '{"command": "crawl", "url": "https://example.com"}'
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_acompletion.return_value = mock_response
        mock_create_config.return_value = LLMConfig()

        result = await parse_command_from_prompt_llm("crawl example.com")
        self.assertEqual(result["command"], "crawl")
        self.assertEqual(result["url"], "https://example.com")
        mock_acompletion.assert_called_once()

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_extract_company_data_from_impressum_llm(self, mock_acompletion, mock_create_config):
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = '{"company_name": "Test GmbH", "city": "Berlin"}'
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_acompletion.return_value = mock_response
        mock_create_config.return_value = LLMConfig()

        result = await extract_company_data_from_impressum_llm("Test impressum content", "https://example.com")
        self.assertIn("company_name", result)
        self.assertEqual(result["company_name"], "Test GmbH")
        mock_acompletion.assert_called_once()

    def test_config_loading(self):
        config = LLMConfig.create_from_ini()
        self.assertIsNotNone(config)
        self.assertIn(config.provider, ["ollama", "openai", "groq"])

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_error_handling(self, mock_acompletion, mock_create_config):
        """Test that LLM errors are handled gracefully."""
        mock_acompletion.side_effect = litellm.exceptions.APIError(
            message="Test error", llm_provider="test", model="test-model", status_code=500)
        mock_create_config.return_value = LLMConfig()

        result = await get_summary_from_llm("Test content")
        self.assertIn("Error generating summary", result)

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_timeout_handling(self, mock_acompletion, mock_create_config):
        """Test that timeout errors are handled gracefully."""
        mock_acompletion.side_effect = litellm.exceptions.Timeout(
            message="Request timed out", model="test-model", llm_provider="test")
        mock_create_config.return_value = LLMConfig()

        result = await get_chat_response_from_llm("Hello")
        self.assertIn("Error communicating with the LLM", result)
