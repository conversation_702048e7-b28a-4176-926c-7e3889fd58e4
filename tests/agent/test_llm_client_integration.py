"""
Integration tests for the new LiteLLM client.
"""

import unittest
from unittest.mock import AsyncMock, patch, MagicMock
import litellm
from agent.llm import get_summary_from_llm, get_chat_response_from_llm
from agent.llm.llm_config import LLMConfig

class TestLLMClientIntegration(unittest.IsolatedAsyncioTestCase):

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_get_summary_from_llm(self, mock_acompletion, mock_create_config):
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Test summary"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_acompletion.return_value = mock_response
        mock_create_config.return_value = LLMConfig()

        result = await get_summary_from_llm("Test content")
        self.assertEqual(result, "Test summary")
        mock_acompletion.assert_called_once()

    @patch('agent.llm.llm_config.LLMConfig.create_from_ini')
    @patch('litellm.acompletion')
    async def test_get_chat_response_from_llm(self, mock_acompletion, mock_create_config):
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Test chat response"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        mock_acompletion.return_value = mock_response
        mock_create_config.return_value = LLMConfig()

        result = await get_chat_response_from_llm("Hello")
        self.assertEqual(result, "Test chat response")
        mock_acompletion.assert_called_once()

    def test_config_loading(self):
        config = LLMConfig.create_from_ini()
        self.assertIsNotNone(config)
        self.assertIn(config.provider, ["ollama", "openai", "groq"])
