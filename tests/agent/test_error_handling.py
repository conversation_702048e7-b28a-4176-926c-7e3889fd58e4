"""
Error handling tests for the Crawl4AI Agent.

These tests verify that the system handles various error conditions gracefully
without crashing and provides meaningful error messages to users.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock
from agent.langgraph_workflow import CrawlAgent
from agent.langgraph_tools import crawl_website_tool, extract_company_data_tool


class TestNetworkErrorHandling:
    """Test handling of network-related errors."""

    @pytest.mark.asyncio
    async def test_invalid_url_handling(self):
        """Test handling of invalid URLs."""
        agent = CrawlAgent()
        
        invalid_urls = [
            "not-a-url",
            "http://",
            "https://",
            "ftp://invalid.com",
            "https://this-domain-definitely-does-not-exist-12345.com"
        ]
        
        for url in invalid_urls:
            result = await agent.process_request(f"crawl {url}")
            assert isinstance(result, dict)
            # Should handle gracefully, not crash

    @pytest.mark.asyncio
    async def test_network_timeout_handling(self):
        """Test handling of network timeouts."""
        # Test with a URL that should timeout (using a non-routable IP)
        result = await crawl_website_tool.ainvoke({
            "url": "http://*********",  # TEST-NET address, should timeout
            "screenshot": False,
            "pdf": False
        })
        
        assert isinstance(result, dict)
        # Should either succeed (if handled) or fail gracefully
        if not result.get("success", True):
            assert "error" in result

    @pytest.mark.asyncio
    async def test_dns_resolution_failure(self):
        """Test handling of DNS resolution failures."""
        result = await crawl_website_tool.ainvoke({
            "url": "https://nonexistent-domain-12345.invalid",
            "screenshot": False,
            "pdf": False
        })
        
        assert isinstance(result, dict)
        # Should handle DNS failure gracefully
        if not result.get("success", True):
            assert "error" in result


class TestLLMErrorHandling:
    """Test handling of LLM-related errors."""

    @pytest.mark.asyncio
    @patch('agent.llm.client.get_chat_response_from_llm')
    async def test_llm_connection_error(self, mock_llm):
        """Test handling of LLM connection errors."""
        # Mock LLM failure
        mock_llm.side_effect = Exception("LLM connection failed")
        
        agent = CrawlAgent()
        result = await agent.process_request("Hello, how are you?")
        
        assert isinstance(result, dict)
        # Should handle LLM failure gracefully

    @pytest.mark.asyncio
    async def test_empty_content_extraction(self):
        """Test handling of empty content for extraction."""
        result = await extract_company_data_tool.ainvoke({
            "content": "",
            "url": "https://example.com"
        })
        
        assert isinstance(result, dict)
        # Should handle empty content gracefully
        if not result.get("success", True):
            assert "error" in result

    @pytest.mark.asyncio
    async def test_malformed_content_extraction(self):
        """Test handling of malformed content for extraction."""
        malformed_content = "This is not an impressum page at all. Just random text."
        
        result = await extract_company_data_tool.ainvoke({
            "content": malformed_content,
            "url": "https://example.com"
        })
        
        assert isinstance(result, dict)
        # Should handle malformed content gracefully


class TestBrowserErrorHandling:
    """Test handling of browser-related errors."""

    @pytest.mark.asyncio
    @patch('crawl4ai.AsyncWebCrawler')
    async def test_browser_launch_failure(self, mock_crawler_class):
        """Test handling of browser launch failures."""
        # Mock browser launch failure
        mock_crawler = MagicMock()
        mock_crawler.__aenter__.side_effect = Exception("Browser launch failed")
        mock_crawler_class.return_value = mock_crawler
        
        result = await crawl_website_tool.ainvoke({
            "url": "https://example.com",
            "screenshot": False,
            "pdf": False
        })
        
        assert isinstance(result, dict)
        assert result.get("success") is False
        assert "error" in result

    @pytest.mark.asyncio
    @patch('crawl4ai.AsyncWebCrawler')
    async def test_page_load_failure(self, mock_crawler_class):
        """Test handling of page load failures."""
        # Mock page load failure
        mock_crawler = MagicMock()
        mock_crawler.__aenter__.return_value = mock_crawler
        mock_crawler.arun.side_effect = Exception("Page load failed")
        mock_crawler_class.return_value = mock_crawler
        
        result = await crawl_website_tool.ainvoke({
            "url": "https://example.com",
            "screenshot": False,
            "pdf": False
        })
        
        assert isinstance(result, dict)
        assert result.get("success") is False
        assert "error" in result


class TestInputValidationErrorHandling:
    """Test handling of input validation errors."""

    @pytest.mark.asyncio
    async def test_empty_input_handling(self):
        """Test handling of empty input."""
        agent = CrawlAgent()
        
        empty_inputs = ["", "   ", "\n\t  \n"]
        
        for empty_input in empty_inputs:
            result = await agent.process_request(empty_input)
            assert isinstance(result, dict)
            # Should handle empty input gracefully

    @pytest.mark.asyncio
    async def test_very_long_input_handling(self):
        """Test handling of very long input."""
        agent = CrawlAgent()
        
        # Create a very long input
        long_input = "crawl " + "a" * 10000 + ".com"
        
        result = await agent.process_request(long_input)
        assert isinstance(result, dict)
        # Should handle long input gracefully

    @pytest.mark.asyncio
    async def test_special_characters_handling(self):
        """Test handling of special characters in input."""
        agent = CrawlAgent()
        
        special_inputs = [
            "crawl https://example.com/path?param=value&other=测试",
            "crawl https://example.com/🚀",
            "crawl https://example.com/path with spaces",
        ]
        
        for special_input in special_inputs:
            result = await agent.process_request(special_input)
            assert isinstance(result, dict)
            # Should handle special characters gracefully


class TestSystemResourceErrorHandling:
    """Test handling of system resource errors."""

    @pytest.mark.asyncio
    async def test_concurrent_requests_handling(self):
        """Test handling of multiple concurrent requests."""
        agent = CrawlAgent()
        
        # Create multiple concurrent requests
        tasks = []
        for i in range(5):
            task = agent.process_request(f"Hello {i}", thread_id=f"thread-{i}")
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should complete without crashing
        assert len(results) == 5
        for result in results:
            if isinstance(result, Exception):
                # If there's an exception, it should be handled gracefully
                assert not isinstance(result, SystemExit)
            else:
                assert isinstance(result, dict)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
