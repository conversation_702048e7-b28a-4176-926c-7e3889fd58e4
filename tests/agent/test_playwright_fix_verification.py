"""
Test to verify that the Playwright browser issue is completely resolved.
"""

import pytest
import asyncio
from agent.langgraph_workflow import Crawl<PERSON>gent
from agent.langgraph_tools import crawl_website_tool


class TestPlaywrightFixVerification:
    """Test that Playwright browser issues are resolved."""

    @pytest.mark.asyncio
    async def test_browser_executable_exists(self):
        """Test that the browser executable can be found."""
        # This should not raise the "Executable doesn't exist" error
        result = await crawl_website_tool.ainvoke({
            "url": "https://httpbin.org/html",
            "screenshot": False,
            "pdf": False
        })
        
        assert isinstance(result, dict)
        # Should either succeed or fail gracefully (not with browser executable error)
        if not result.get("success", True):
            error_msg = result.get("error", "")
            # Should NOT contain the specific browser executable error
            assert "Executable doesn't exist" not in error_msg
            assert "chromium-1169" not in error_msg

    @pytest.mark.asyncio
    async def test_successful_crawling_works(self):
        """Test that successful crawling works with the fixed browser."""
        result = await crawl_website_tool.ainvoke({
            "url": "https://httpbin.org/html",
            "screenshot": False,
            "pdf": False
        })
        
        assert isinstance(result, dict)
        assert result.get("success") is True
        assert "markdown_content" in result
        assert len(result["markdown_content"]) > 0

    @pytest.mark.asyncio
    async def test_timeout_handling_works(self):
        """Test that network timeouts are handled gracefully."""
        # Use a URL that should timeout
        result = await crawl_website_tool.ainvoke({
            "url": "https://httpbin.org/delay/70",  # 70 second delay, should timeout
            "screenshot": False,
            "pdf": False
        })
        
        assert isinstance(result, dict)
        # Should handle timeout gracefully
        if not result.get("success", True):
            error_msg = result.get("error", "")
            # Should be a timeout error, not a browser executable error
            assert "Executable doesn't exist" not in error_msg
            assert "chromium-1169" not in error_msg

    @pytest.mark.asyncio
    async def test_end_to_end_workflow_works(self):
        """Test that the complete workflow works without browser errors."""
        agent = CrawlAgent()
        
        # Test a simple request
        result = await agent.process_request("crawl https://httpbin.org/html")
        
        assert isinstance(result, dict)
        # Should not contain browser executable errors
        if result.get("error"):
            error_msg = result["error"]
            assert "Executable doesn't exist" not in error_msg
            assert "chromium-1169" not in error_msg

    @pytest.mark.asyncio
    async def test_multiple_concurrent_requests(self):
        """Test that multiple concurrent requests work without browser issues."""
        agent = CrawlAgent()
        
        # Create multiple concurrent requests
        tasks = []
        for i in range(3):
            task = agent.process_request(f"crawl https://httpbin.org/html", thread_id=f"test-{i}")
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should complete without browser executable errors
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Should not be browser executable errors
                error_msg = str(result)
                assert "Executable doesn't exist" not in error_msg
                assert "chromium-1169" not in error_msg
            else:
                assert isinstance(result, dict)
                if result.get("error"):
                    error_msg = result["error"]
                    assert "Executable doesn't exist" not in error_msg
                    assert "chromium-1169" not in error_msg

    @pytest.mark.asyncio
    async def test_browser_launch_stability(self):
        """Test that browser launch is stable across multiple requests."""
        # Make several requests in sequence to test browser stability
        urls = [
            "https://httpbin.org/html",
            "https://httpbin.org/json",
            "https://httpbin.org/xml"
        ]
        
        for url in urls:
            result = await crawl_website_tool.ainvoke({
                "url": url,
                "screenshot": False,
                "pdf": False
            })
            
            assert isinstance(result, dict)
            # Should not have browser executable errors
            if not result.get("success", True):
                error_msg = result.get("error", "")
                assert "Executable doesn't exist" not in error_msg
                assert "chromium-1169" not in error_msg


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
