"""
Quick test of the LangGraph agent functionality.
"""

import asyncio
import pytest
from agent.langgraph_workflow import CrawlAgent


@pytest.mark.asyncio
async def test_agent():
    """Test the agent with a simple request."""
    print("🚀 Testing LangGraph Agent...")
    
    try:
        agent = CrawlAgent()
        print("✅ Agent initialized successfully")
        
        # Test a simple chat request
        result = await agent.process_request("what can you do?")
        
        assert "error" not in result, f"Agent returned an error: {result.get('error')}"
        assert "response" in result, "Agent did not return a response."
        print(f"✅ Agent responded successfully: {result['response'][:200]}...")

    except Exception as e:
        pytest.fail(f"An exception occurred during the agent test: {e}")


if __name__ == "__main__":
    success = asyncio.run(test_agent())
    if success:
        print("\n🎉 LangGraph agent is working! You can now run:")
        print("  python main_langgraph.py")
    else:
        print("\n⚠️  There may be configuration issues. Check:")
        print("  • <PERSON><PERSON><PERSON> is running (ollama serve)")
        print("  • Model is available (ollama pull deepseek-coder)")
        print("  • config.ini is properly configured")