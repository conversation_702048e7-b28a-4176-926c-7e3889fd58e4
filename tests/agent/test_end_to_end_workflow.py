"""
End-to-end workflow tests for the Crawl4AI Agent.

These tests verify that the complete workflow from user input to final output
works correctly with real crawling and LLM processing.
"""

import pytest
import asyncio
from agent.langgraph_workflow import CrawlAgent


class TestEndToEndWorkflow:
    """Test complete end-to-end workflows."""

    @pytest.mark.asyncio
    async def test_simple_crawl_workflow(self):
        """Test a simple crawl workflow end-to-end."""
        agent = CrawlAgent()
        
        # Test a simple crawl request
        result = await agent.process_request("crawl https://httpbin.org/html")
        
        # Verify the result structure
        assert isinstance(result, dict)
        assert "error" not in result or result.get("success") is not False
        
        # Should have crawled content
        if "markdown_content" in result:
            assert len(result["markdown_content"]) > 0
        elif "final_result" in result:
            assert result["final_result"] is not None

    @pytest.mark.asyncio
    async def test_impressum_crawl_workflow(self):
        """Test impressum crawling workflow end-to-end."""
        agent = CrawlAgent()
        
        # Test impressum crawling (may not find impressum but should handle gracefully)
        result = await agent.process_request("crawl imprint from httpbin.org")
        
        # Verify the result structure
        assert isinstance(result, dict)
        
        # Should either succeed or fail gracefully
        if result.get("success") is False:
            assert "error" in result
        else:
            # If successful, should have some content
            assert "markdown_content" in result or "final_result" in result

    @pytest.mark.asyncio
    async def test_chat_workflow(self):
        """Test chat workflow end-to-end."""
        agent = CrawlAgent()
        
        # Test a simple chat request
        result = await agent.process_request("Hello, how are you?")
        
        # Verify the result structure
        assert isinstance(result, dict)
        
        # Should have a chat response
        if "chat_response" in result:
            assert len(result["chat_response"]) > 0
        elif "final_result" in result:
            assert result["final_result"] is not None

    @pytest.mark.asyncio
    async def test_error_handling_workflow(self):
        """Test error handling in the workflow."""
        agent = CrawlAgent()
        
        # Test with an invalid URL
        result = await agent.process_request("crawl https://this-domain-does-not-exist-12345.com")
        
        # Verify error handling
        assert isinstance(result, dict)
        # Should either handle the error gracefully or report it
        if result.get("success") is False:
            assert "error" in result
        # The workflow should not crash

    @pytest.mark.asyncio
    async def test_multiple_requests_workflow(self):
        """Test multiple requests to ensure the agent handles them correctly."""
        agent = CrawlAgent()
        
        # Test multiple requests in sequence
        requests = [
            "Hello",
            "crawl https://httpbin.org/html",
            "What did you find?"
        ]
        
        results = []
        for request in requests:
            result = await agent.process_request(request, thread_id="test-thread")
            results.append(result)
            assert isinstance(result, dict)
        
        # All requests should complete without crashing
        assert len(results) == 3

    @pytest.mark.asyncio
    async def test_workflow_with_real_llm(self):
        """Test workflow with real LLM processing."""
        agent = CrawlAgent()
        
        # Test a request that requires LLM processing
        result = await agent.process_request("summarize https://httpbin.org/html")
        
        # Verify the result
        assert isinstance(result, dict)
        
        # Should have processed the content
        if result.get("success") is not False:
            # Should have some kind of summary or processed content
            has_content = any(key in result for key in [
                "summary", "markdown_content", "final_result", "chat_response"
            ])
            assert has_content, f"No content found in result: {list(result.keys())}"


class TestWorkflowErrorRecovery:
    """Test error recovery in workflows."""

    @pytest.mark.asyncio
    async def test_network_error_recovery(self):
        """Test recovery from network errors."""
        agent = CrawlAgent()
        
        # Test with a URL that should cause a network error
        result = await agent.process_request("crawl https://*********")  # TEST-NET address
        
        # Should handle the error gracefully
        assert isinstance(result, dict)
        # Should not crash the entire system

    @pytest.mark.asyncio
    async def test_invalid_input_recovery(self):
        """Test recovery from invalid input."""
        agent = CrawlAgent()
        
        # Test with various invalid inputs
        invalid_inputs = [
            "",  # Empty string
            "   ",  # Whitespace only
            "crawl",  # Missing URL
            "crawl not-a-url",  # Invalid URL format
        ]
        
        for invalid_input in invalid_inputs:
            result = await agent.process_request(invalid_input)
            assert isinstance(result, dict)
            # Should handle gracefully, not crash


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
