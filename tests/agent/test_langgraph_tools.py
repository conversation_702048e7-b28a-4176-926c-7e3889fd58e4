"""
Tests for LangGraph tools.

This module tests the LangGraph tool functions that wrap the crawler
functionality for use in the agentic workflow.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock

# Import the tools to test
from agent.langgraph_tools import (
    crawl_website_tool,
    crawl_with_links_tool,
    crawl_multiple_websites_tool,
    crawl_multiple_impressums_tool,
    crawl_impressum_tool,

    web_search_tool,
    search_and_crawl_tool,
    extract_company_data_tool,

)


class TestCrawlWebsiteTool:
    """Test the crawl_website_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_website', new_callable=MagicMock)
    async def test_crawl_website_tool_success(self, mock_crawl_website):
        """Test successful website crawling via tool.""" 
        mock_crawl_website.return_value = asyncio.Future()
        mock_crawl_website.return_value.set_result((
            "# Test Content", "/path/to/file.html", None, None, None, "Test Page"
        ))

        result = await crawl_website_tool.ainvoke({
            "url": "https://www.example.com",
            "screenshot": False,
            "pdf": False
        })

        # Verify result structure
        assert isinstance(result, dict)
        assert result["success"] is True
        assert result["markdown_content"] == "# Test Content"
        assert result["saved_html_path"] == "/path/to/file.html"

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_website', new_callable=MagicMock)
    async def test_crawl_website_tool_with_options(self, mock_crawl_website):
        """Test website crawling tool with screenshot and PDF."""
        mock_crawl_website.return_value = asyncio.Future()
        mock_crawl_website.return_value.set_result((
            "# Test Content", "/path/to/file.html", "/path/to/screenshot.png", "/path/to/file.pdf", None, "Test Page"
        ))

        result = await crawl_website_tool.ainvoke({
            "url": "https://www.example.com",
            "screenshot": True,
            "pdf": True
        })

        # Verify additional files were created
        assert result["screenshot_path"] == "/path/to/screenshot.png"
        assert result["pdf_path"] == "/path/to/file.pdf"
        
    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_website', new_callable=MagicMock)
    async def test_crawl_website_tool_invalid_url(self, mock_crawl_website):
        """Test website crawling tool with invalid URL."""
        mock_crawl_website.return_value = asyncio.Future()
        # Simulate crawl_website returning an error tuple
        mock_crawl_website.return_value.set_result(("Error: Invalid URL", None, None, None, None, None))

        result = await crawl_website_tool.ainvoke({
            "url": "invalid-url",
        })

        # Should return error
        assert "error" in result
        assert result["error"] == "Error: Invalid URL"


class TestCrawlWithLinksTool:
    """Test the crawl_with_links_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_website', new_callable=MagicMock)
    @patch('agent.langgraph_tools.save_links_to_file', new_callable=MagicMock)
    async def test_crawl_with_links_tool_success(self, mock_save_links, mock_crawl_website):
        """Test successful link extraction via tool."""
        mock_crawl_website.return_value = asyncio.Future()
        mock_crawl_website.return_value.set_result((
            "# Test Content", "/path/to/file.html", None, None,
            {"internal": ["/page1"], "external": ["https://google.com"]},
            "Test Page"
        ))
        mock_save_links.return_value = "/path/to/links.json"

        result = await crawl_with_links_tool.ainvoke({
            "url": "https://www.example.com",
            "screenshot": False,
            "pdf": False,
            "extract_links": True,
            "save_links": True
        })

        # Verify link extraction results
        assert isinstance(result, dict)
        assert result["success"] is True
        assert "links" in result
        assert result["links_file_path"] == "/path/to/links.json"
        assert result["links"]["internal"] == ["/page1"]


class TestCrawlMultipleWebsitesTool:
    """Test the crawl_multiple_websites_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_multiple_websites', new_callable=MagicMock)
    async def test_crawl_multiple_websites_tool_list(self, mock_crawl):
        """Test multiple website crawling with list of URLs."""
        mock_crawl.return_value = asyncio.Future()
        mock_crawl.return_value.set_result([
            {"success": True, "url": "https://www.example.com", "data": "..."},
            {"success": True, "url": "https://www.google.com", "data": "..."}
        ])

        urls = ["https://www.example.com", "https://www.google.com"]
        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": urls,
            "screenshot": False,
            "pdf": False,
            "extract_links": False
        })

        # Verify batch results
        assert isinstance(result, dict)
        assert result["total_urls"] == 2
        assert result["successful_crawls"] == 2
        assert result["failed_crawls"] == 0
        assert len(result["results"]) == 2
        
    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_multiple_websites', new_callable=MagicMock)
    async def test_crawl_multiple_websites_tool_single_string(self, mock_crawl):
        """Test multiple website crawling with single URL string."""
        mock_crawl.return_value = asyncio.Future()
        mock_crawl.return_value.set_result([{"success": True, "url": "https://www.example.com"}])

        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": "https://www.example.com",
            "screenshot": False,
            "pdf": False,
            "extract_links": False
        })

        # Should handle single URL as string
        assert result["total_urls"] == 1
        assert len(result["results"]) == 1
        
    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_multiple_websites', new_callable=MagicMock)
    async def test_crawl_multiple_websites_tool_with_links(self, mock_crawl):
        """Test multiple website crawling with link extraction."""
        mock_crawl.return_value = asyncio.Future()
        mock_crawl.return_value.set_result([
            {"success": True, "url": "https://www.example.com", "links_data": {"internal": [], "external": []}}
        ])

        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": ["https://www.example.com"],
            "screenshot": False,
            "pdf": False,
            "extract_links": True
        })

        # Verify link extraction in batch results
        assert result["total_urls"] == 1
        assert result["successful_crawls"] > 0
        first_result = result["results"][0]
        assert first_result["success"] is True
        assert first_result["links_data"] is not None


class TestCrawlMultipleImpressumsTool:
    """Test the crawl_multiple_impressums_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_multiple_impressums', new_callable=MagicMock)
    async def test_crawl_multiple_impressums_tool_list(self, mock_crawl):
        """Test multiple impressum crawling with list of URLs."""
        mock_crawl.return_value = asyncio.Future()
        mock_crawl.return_value.set_result([
            {"success": True, "url": "https://www.example.com", "content": "..."},
            {"success": True, "url": "https://www.raumweltenheiss.de", "content": "..."}
        ])

        urls = ["https://www.example.com", "https://www.raumweltenheiss.de"]
        result = await crawl_multiple_impressums_tool.ainvoke({
            "urls": urls
        })

        # Verify batch impressum results
        assert isinstance(result, dict)
        assert result["total_urls"] == 2
        assert result["successful_crawls"] == 2
        assert len(result["results"]) == 2
        
    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_multiple_impressums', new_callable=MagicMock)
    async def test_crawl_multiple_impressums_tool_single_string(self, mock_crawl):
        """Test multiple impressum crawling with single URL string."""
        mock_crawl.return_value = asyncio.Future()
        mock_crawl.return_value.set_result([{"success": True, "url": "https://www.raumweltenheiss.de"}])

        result = await crawl_multiple_impressums_tool.ainvoke({
            "urls": "https://www.raumweltenheiss.de"
        })

        # Should handle single URL as string
        assert result["total_urls"] == 1
        assert len(result["results"]) == 1


class TestCrawlImpressumTool:
    """Test the crawl_impressum_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.crawl_impressum', new_callable=MagicMock)
    async def test_crawl_impressum_tool_success(self, mock_crawl_impressum):
        """Test successful impressum crawling via tool."""
        mock_crawl_impressum.return_value = asyncio.Future()
        mock_crawl_impressum.return_value.set_result(
            ("Impressum content", "/path/to/impressum.html")
        )

        result = await crawl_impressum_tool.ainvoke({
            "base_url": "https://www.raumweltenheiss.de"
        })

        # Verify impressum result structure
        assert isinstance(result, dict)
        assert result["success"] is True
        assert result["markdown_content"] == "Impressum content"
        assert result["saved_html_path"] == "/path/to/impressum.html"





class TestWebSearchTool:
    """Test the web_search_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.DDGS')
    async def test_web_search_tool_success(self, MockDDGS):
        """Test successful web search via tool."""
        mock_instance = MockDDGS.return_value
        mock_instance.__enter__.return_value.text.return_value = [
            {'title': 'Python Web Scraping', 'href': 'https://example.com/scrape', 'body': '...'},
            {'title': 'Beautiful Soup', 'href': 'https://crummy.com', 'body': '...'}
        ]

        result = await web_search_tool.ainvoke({
            "query": "python web scraping",
            "max_results": 3
        })

        # Verify search results
        assert isinstance(result, dict)
        assert "results" in result
        assert isinstance(result["results"], list)
        assert len(result["results"]) == 2

        # Verify result structure
        first_result = result["results"][0]
        assert "title" in first_result
        assert "url" in first_result
        assert "snippet" in first_result


class TestSearchAndCrawlTool:
    """Test the search_and_crawl_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.DDGS')
    @patch('agent.langgraph_tools.crawl_multiple_websites', new_callable=MagicMock)
    async def test_search_and_crawl_tool_success(self, mock_crawl, MockDDGS):
        """Test successful search and crawl via tool."""
        mock_instance = MockDDGS.return_value
        mock_instance.__enter__.return_value.text.return_value = [
            {'title': 'Python Docs', 'href': 'https://docs.python.org', 'body': '...'}
        ]
        mock_crawl.return_value = asyncio.Future()
        mock_crawl.return_value.set_result([
            {"success": True, "url": "https://docs.python.org", "markdown_content": "..."}
        ])

        result = await search_and_crawl_tool.ainvoke({
            "query": "python documentation",
            "max_results": 1
        })

        # Verify search and crawl results
        assert isinstance(result, dict)
        assert "search_results" in result
        assert "crawled_results" in result
        assert len(result["search_results"]) == 1
        assert len(result["crawled_results"]) == 1
        assert "markdown_content" in result["crawled_results"][0]


class TestExtractCompanyDataTool:
    """Test the extract_company_data_tool."""

    @pytest.mark.asyncio
    @patch('agent.langgraph_tools.extract_company_data_from_impressum', new_callable=MagicMock)
    @patch('agent.langgraph_tools.save_json_to_dir', new_callable=MagicMock)
    async def test_extract_company_data_tool_success(self, mock_save_json, mock_extract_data):
        """Test company data extraction via tool."""
        mock_extract_data.return_value = asyncio.Future()
        mock_extract_data.return_value.set_result({"company_name": "Test GmbH"})
        mock_save_json.return_value = "/path/to/company_data.json"

        result = await extract_company_data_tool.ainvoke({
            "content": "Impressum...",
            "url": "https://www.test.de"
        })

        # Verify extraction result
        assert isinstance(result, dict)
        assert result["success"] is True
        assert result["company_data"] == {"company_name": "Test GmbH"}
        assert result["json_file_path"] == "/path/to/company_data.json"





if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
