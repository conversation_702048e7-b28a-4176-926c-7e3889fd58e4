"""
Tests for HTML download functionality in LangGraph agent.

This module tests the LangGraph integration of HTML download features,
including intent parsing, workflow routing, and tool execution.
"""

import pytest
import asyncio
import os
import tempfile
from unittest.mock import patch, MagicMock

# Import LangGraph components
from agent.langgraph_state import create_initial_state, WorkflowCommands
from agent.langgraph_nodes import intent_parser_node, download_html_node, download_multiple_html_node
from agent.langgraph_tools import download_html_tool, download_multiple_html_tool


class TestHtmlDownloadIntentParsing:
    """Test HTML download intent parsing."""

    @pytest.mark.asyncio
    async def test_intent_parsing_single_html_download(self):
        """Test intent parsing for single HTML download commands."""
        test_prompts = [
            "download html from example.com",
            "save html from https://example.com",
            "get html from example.com",
            "download page from example.com"
        ]

        for prompt in test_prompts:
            state = create_initial_state(prompt)
            result = await intent_parser_node(state)

            # Verify intent parsing
            assert result['parsed_intent']['command'] == 'download_html'
            assert result['next_action'] == WorkflowCommands.DOWNLOAD_HTML
            assert result['url'] is not None
            assert 'example.com' in result['url']

    @pytest.mark.asyncio
    async def test_intent_parsing_html_download_with_screenshot(self):
        """Test intent parsing for HTML download with screenshot."""
        test_prompts = [
            "download html from example.com --screenshot",
            "save html from https://example.com --screenshot",
            "get html from example.com --screenshot --pdf"
        ]

        for prompt in test_prompts:
            state = create_initial_state(prompt)
            result = await intent_parser_node(state)

            # Verify intent parsing
            assert result['parsed_intent']['command'] == 'download_html'
            assert result['next_action'] == WorkflowCommands.DOWNLOAD_HTML
            assert result['url'] is not None
            assert result['screenshot'] is True
            if '--pdf' in prompt:
                assert result['pdf'] is True

    @pytest.mark.asyncio
    async def test_intent_parsing_multiple_html_download(self):
        """Test intent parsing for multiple HTML download commands."""
        test_prompts = [
            "download html from example.com,httpbin.org/html",
            "save html from example.com, google.com",
            "get html from example.com,python.org"
        ]

        for prompt in test_prompts:
            state = create_initial_state(prompt)
            result = await intent_parser_node(state)

            # Verify intent parsing for multiple URLs
            assert result['parsed_intent']['command'] == 'download_multiple_html'
            assert result['next_action'] == WorkflowCommands.DOWNLOAD_MULTIPLE_HTML
            assert result['urls'] is not None
            assert len(result['urls']) >= 2

    @pytest.mark.asyncio
    async def test_intent_parsing_multiple_html_download_with_media(self):
        """Test intent parsing for multiple HTML download with screenshot and PDF."""
        test_prompts = [
            "download html from example.com,httpbin.org/html --screenshot",
            "save html from example.com, google.com --pdf",
            "get html from example.com,python.org --screenshot --pdf"
        ]

        for prompt in test_prompts:
            state = create_initial_state(prompt)
            result = await intent_parser_node(state)

            # Verify intent parsing
            assert result['parsed_intent']['command'] == 'download_multiple_html'
            assert result['next_action'] == WorkflowCommands.DOWNLOAD_MULTIPLE_HTML
            assert result['urls'] is not None
            assert len(result['urls']) >= 2

            if '--screenshot' in prompt:
                assert result['screenshot'] is True
            if '--pdf' in prompt:
                assert result['pdf'] is True

    @pytest.mark.asyncio
    async def test_intent_parsing_invalid_html_download(self):
        """Test intent parsing for invalid HTML download commands."""
        test_prompts = [
            "download html",  # No URL
            "download html from",  # No URL after 'from'
            "save html content"  # No URL specified
        ]

        for prompt in test_prompts:
            state = create_initial_state(prompt)
            result = await intent_parser_node(state)

            # Should either error or not recognize as HTML download
            if result.get('error'):
                assert 'url' in result['error'].lower(
                ) or 'found' in result['error'].lower()
            else:
                # Should not be recognized as HTML download
                assert result['parsed_intent']['command'] != 'download_html'


class TestHtmlDownloadTools:
    """Test HTML download LangGraph tools."""

    @pytest.mark.asyncio
    async def test_download_html_tool_success(self):
        """Test successful HTML download tool execution."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_html_tool.ainvoke({
                "url": "https://www.example.com",
                "html_dir": temp_dir,
                "wait_until": "networkidle",
                "page_timeout": 30000
            })

            # Verify tool result
            assert result['success'] is True
            assert result['url'] == "https://www.example.com"
            assert result['html_path'] is not None
            assert os.path.exists(result['html_path'])
            assert result['html_path'].startswith(temp_dir)
            assert result['page_title'] is not None

    @pytest.mark.asyncio
    async def test_download_html_tool_failure(self):
        """Test HTML download tool with invalid URL."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_html_tool.ainvoke({
                "url": "https://invalid-domain-that-does-not-exist-12345.com",
                "html_dir": temp_dir,
                "wait_until": "networkidle",
                "page_timeout": 10000
            })

            # Verify tool handles error
            assert 'error' in result
            assert result['error'] is not None

    @pytest.mark.asyncio
    async def test_download_html_tool_with_screenshot(self):
        """Test download_html_tool with screenshot option."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_html_tool.ainvoke({
                "url": "https://www.example.com",
                "html_dir": temp_dir,
                "screenshot": True,
                "pdf": False
            })

            # Verify tool result
            if result.get('success'):
                assert 'html_path' in result
                assert result['html_path'] is not None
                # Screenshot path may be None if screenshot capture fails
                assert 'screenshot_path' in result

    @pytest.mark.asyncio
    async def test_download_html_tool_with_pdf(self):
        """Test download_html_tool with PDF option."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_html_tool.ainvoke({
                "url": "https://www.example.com",
                "html_dir": temp_dir,
                "screenshot": False,
                "pdf": True
            })

            # Verify tool result
            if result.get('success'):
                assert 'html_path' in result
                assert result['html_path'] is not None
                # PDF path may be None if PDF generation fails
                assert 'pdf_path' in result

    @pytest.mark.asyncio
    async def test_download_html_tool_with_both_media(self):
        """Test download_html_tool with both screenshot and PDF options."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_html_tool.ainvoke({
                "url": "https://www.example.com",
                "html_dir": temp_dir,
                "screenshot": True,
                "pdf": True
            })

            # Verify tool result
            if result.get('success'):
                assert 'html_path' in result
                assert result['html_path'] is not None
                assert 'screenshot_path' in result
                assert 'pdf_path' in result

    @pytest.mark.asyncio
    async def test_download_multiple_html_tool_success(self):
        """Test successful multiple HTML download tool execution."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]

        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_multiple_html_tool.ainvoke({
                "urls": urls,
                "html_dir": temp_dir,
                "wait_until": "networkidle",
                "page_timeout": 30000
            })

            # Verify tool result
            assert result['success'] is True
            assert result['total_urls'] == len(urls)
            assert result['successful_downloads'] >= 1
            assert 'results' in result
            assert len(result['results']) == len(urls)

    @pytest.mark.asyncio
    async def test_download_multiple_html_tool_mixed_results(self):
        """Test multiple HTML download tool with mixed valid/invalid URLs."""
        urls = [
            "https://www.example.com",
            "https://invalid-domain-that-does-not-exist-12345.com"
        ]

        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_multiple_html_tool.ainvoke({
                "urls": urls,
                "html_dir": temp_dir,
                "wait_until": "networkidle",
                "page_timeout": 15000
            })

            # Verify tool handles mixed results
            assert result['total_urls'] == len(urls)
            assert result['successful_downloads'] >= 1
            assert result['failed_downloads'] >= 1
            assert 'failed_urls' in result
            assert 'errors' in result

    @pytest.mark.asyncio
    async def test_download_multiple_html_tool_with_screenshot(self):
        """Test multiple HTML download tool with screenshot option."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]

        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_multiple_html_tool.ainvoke({
                "urls": urls,
                "html_dir": temp_dir,
                "screenshot": True,
                "pdf": False
            })

            # Verify tool result
            if result.get('success'):
                assert result['total_urls'] == len(urls)
                assert 'results' in result
                # Check that screenshot_path is included in results
                for download_result in result['results']:
                    if download_result['success']:
                        assert 'screenshot_path' in download_result

    @pytest.mark.asyncio
    async def test_download_multiple_html_tool_with_pdf(self):
        """Test multiple HTML download tool with PDF option."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]

        with tempfile.TemporaryDirectory() as temp_dir:
            result = await download_multiple_html_tool.ainvoke({
                "urls": urls,
                "html_dir": temp_dir,
                "screenshot": False,
                "pdf": True
            })

            # Verify tool result
            if result.get('success'):
                assert result['total_urls'] == len(urls)
                assert 'results' in result
                # Check that pdf_path is included in results
                for download_result in result['results']:
                    if download_result['success']:
                        assert 'pdf_path' in download_result


class TestHtmlDownloadNodes:
    """Test HTML download workflow nodes."""

    @pytest.mark.asyncio
    async def test_download_html_node_success(self):
        """Test successful HTML download node execution."""
        with tempfile.TemporaryDirectory() as temp_dir:
            state = {
                'url': 'https://www.example.com',
                'html_dir': temp_dir
            }

            result = await download_html_node(state)

            # Verify node result
            assert result['next_action'] == WorkflowCommands.FORMAT_OUTPUT
            assert result['saved_html_path'] is not None
            assert os.path.exists(result['saved_html_path'])
            assert result['page_title'] is not None
            assert 'error' not in result

    @pytest.mark.asyncio
    async def test_download_html_node_failure(self):
        """Test HTML download node with invalid URL."""
        state = {
            'url': 'https://invalid-domain-that-does-not-exist-12345.com'
        }

        result = await download_html_node(state)

        # Verify node handles error
        assert result['next_action'] == WorkflowCommands.ERROR
        assert 'error' in result

    @pytest.mark.asyncio
    async def test_download_html_node_with_screenshot(self):
        """Test HTML download node with screenshot option."""
        with tempfile.TemporaryDirectory() as temp_dir:
            state = {
                'url': 'https://www.example.com',
                'html_dir': temp_dir,
                'screenshot': True,
                'pdf': False
            }

            result = await download_html_node(state)

            # Verify node result
            if result.get('next_action') == WorkflowCommands.FORMAT_OUTPUT:
                assert result['saved_html_path'] is not None
                assert 'screenshot_path' in result
                # Screenshot path may be None if capture fails

    @pytest.mark.asyncio
    async def test_download_html_node_with_pdf(self):
        """Test HTML download node with PDF option."""
        with tempfile.TemporaryDirectory() as temp_dir:
            state = {
                'url': 'https://www.example.com',
                'html_dir': temp_dir,
                'screenshot': False,
                'pdf': True
            }

            result = await download_html_node(state)

            # Verify node result
            if result.get('next_action') == WorkflowCommands.FORMAT_OUTPUT:
                assert result['saved_html_path'] is not None
                assert 'pdf_path' in result
                # PDF path may be None if generation fails
                assert result['pdf_path'] is not None

    @pytest.mark.asyncio
    async def test_download_multiple_html_node_success(self):
        """Test successful multiple HTML download node execution."""
        urls = ["https://www.example.com", "https://httpbin.org/html"]

        with tempfile.TemporaryDirectory() as temp_dir:
            state = {
                'urls': urls,
                'html_dir': temp_dir
            }

            result = await download_multiple_html_node(state)

            # Verify node result
            assert result['next_action'] == WorkflowCommands.FORMAT_OUTPUT
            assert result['html_download_results'] is not None
            assert result['successful_downloads'] >= 1
            assert 'error' not in result


class TestHtmlDownloadWorkflowIntegration:
    """Test HTML download workflow integration."""

    @pytest.mark.asyncio
    async def test_html_download_workflow_routing(self):
        """Test that HTML download commands are routed correctly."""
        from agent.langgraph_workflow import _route_after_intent_parsing

        # Test single HTML download routing
        state = {
            'next_action': WorkflowCommands.DOWNLOAD_HTML,
            'url': 'https://example.com'
        }
        route = _route_after_intent_parsing(state)
        assert route == WorkflowCommands.DOWNLOAD_HTML

        # Test multiple HTML download routing
        state = {
            'next_action': WorkflowCommands.DOWNLOAD_MULTIPLE_HTML,
            'urls': ['https://example.com', 'https://google.com']
        }
        route = _route_after_intent_parsing(state)
        assert route == WorkflowCommands.DOWNLOAD_MULTIPLE_HTML

    @pytest.mark.asyncio
    async def test_html_download_error_routing(self):
        """Test error routing for HTML download commands."""
        from agent.langgraph_workflow import _route_after_intent_parsing

        # Test error routing
        state = {
            'error': 'Failed to download HTML',
            'next_action': WorkflowCommands.DOWNLOAD_HTML
        }
        route = _route_after_intent_parsing(state)
        assert route == WorkflowCommands.ERROR


class TestHtmlDownloadResultFormatting:
    """Test HTML download result formatting."""

    @pytest.mark.asyncio
    async def test_html_download_result_formatting(self):
        """Test that HTML download results are formatted correctly."""
        from agent.langgraph_nodes import result_formatter_node

        # Test single HTML download result formatting
        state = {
            'parsed_intent': {'command': 'download_html'},
            'next_action': WorkflowCommands.DOWNLOAD_HTML,
            'saved_html_path': '/path/to/file.html',
            'page_title': 'Test Page',
            'url': 'https://example.com',
            'html_dir': 'scraped_html'
        }

        result = await result_formatter_node(state)

        # Verify result formatting
        assert result['result'] is not None
        assert 'files' in result['result']
        assert 'html' in result['result']['files']
        assert result['result']['page_title'] == 'Test Page'
        assert result['result']['downloaded_url'] == 'https://example.com'
        assert result['result']['message'] == 'HTML content successfully downloaded and saved'

    @pytest.mark.asyncio
    async def test_multiple_html_download_result_formatting(self):
        """Test that multiple HTML download results are formatted correctly."""
        from agent.langgraph_nodes import result_formatter_node

        # Test multiple HTML download result formatting
        state = {
            'parsed_intent': {'command': 'download_multiple_html'},
            'next_action': WorkflowCommands.DOWNLOAD_MULTIPLE_HTML,
            'html_download_results': [
                {'url': 'https://example.com', 'success': True,
                    'html_path': '/path/to/file1.html'},
                {'url': 'https://google.com', 'success': True,
                    'html_path': '/path/to/file2.html'}
            ],
            'successful_downloads': 2,
            'failed_downloads': 0,
            'html_dir': 'scraped_html'
        }

        result = await result_formatter_node(state)

        # Verify result formatting
        assert result['result'] is not None
        assert 'html_download_results' in result['result']
        assert len(result['result']['html_download_results']) == 2


class TestStateKeyHandling:
    """Test that new state keys are properly handled."""

    @pytest.mark.asyncio
    async def test_state_keys_for_multiple_url_operations(self):
        """Test that state properly handles multiple URL operation keys."""
        from agent.langgraph_state import CrawlAgentState

        # Test that the new state keys are defined
        state_annotations = CrawlAgentState.__annotations__

        # Check for multiple crawl result keys
        assert 'multiple_crawl_results' in state_annotations
        assert 'successful_crawls' in state_annotations
        assert 'failed_crawls' in state_annotations

        # Check for summary generation keys
        assert 'successful_summaries' in state_annotations
        assert 'failed_summaries' in state_annotations

    @pytest.mark.asyncio
    async def test_create_initial_state_with_summarization(self):
        """Test create_initial_state with summarization prompt."""
        prompt = "summarize https://example.com, https://httpbin.org/html"
        state = create_initial_state(prompt)

        # Verify initial state structure
        assert state['user_prompt'] == prompt
        assert state.get('multiple_crawl_results') is None
        assert state.get('successful_crawls') is None
        assert state.get('failed_crawls') is None
        assert state.get('successful_summaries') is None
        assert state.get('failed_summaries') is None
