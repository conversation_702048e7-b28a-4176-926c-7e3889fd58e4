import pytest
from agent.langgraph_nodes import intent_parser_node
from agent.langgraph_state import WorkflowCommands
import asyncio

class DummyState(dict):
    pass

@pytest.mark.asyncio
@pytest.mark.parametrize("prompt,expected_region,expected_action", [
    ("search for python", "us-en", WorkflowCommands.WEB_SEARCH),
    ("search for KI Forschung --lang de", "de-de", WorkflowCommands.WEB_SEARCH),
    ("search for IA --lang es", "es-es", WorkflowCommands.WEB_SEARCH),
    ("search for IA --region fr-fr", "fr-fr", WorkflowCommands.WEB_SEARCH),
    ("search for AI --lang pl", "pl-pl", WorkflowCommands.WEB_SEARCH),
    ("search for AI --lang xx", "xx-xx", WorkflowCommands.WEB_SEARCH),
    ("search for AI --lang de --region fr-fr", "fr-fr", WorkflowCommands.WEB_SEARCH),
])
async def test_intent_parser_node_region(prompt, expected_region, expected_action):
    state = DummyState(user_prompt=prompt)
    result_state = await intent_parser_node(state)
    # The region should be parsed and available in state if specified
    if "--lang" in prompt or "--region" in prompt:
        # The region is parsed in the intent parser, but needs to be propagated to the next node
        # Let's check if it's in the parsed intent or state
        region = result_state.get("region")
        if not region and "parsed_intent" in result_state:
            region = result_state["parsed_intent"].get("region")
        assert region == expected_region, f"Prompt: {prompt} | Got: {region} | Expected: {expected_region}"
    # The next_action should be WEB_SEARCH for search queries
    assert result_state["next_action"] == expected_action

@pytest.mark.asyncio
async def test_intent_parser_node_with_multiple_flags():
    state = DummyState(user_prompt="search for Data Science --lang de --screenshot --pdf --region fr-fr")
    result_state = await intent_parser_node(state)
    # Should prefer explicit region over lang
    assert result_state.get("region") == "fr-fr"
    assert result_state["screenshot"] is True
    assert result_state["pdf"] is True
    assert result_state["next_action"] == WorkflowCommands.WEB_SEARCH

@pytest.mark.asyncio
async def test_intent_parser_node_no_region():
    state = DummyState(user_prompt="search for Data Science")
    result_state = await intent_parser_node(state)
    # Should default to us-en
    assert result_state.get("region", "us-en") == "us-en"
    assert result_state["next_action"] == WorkflowCommands.WEB_SEARCH
