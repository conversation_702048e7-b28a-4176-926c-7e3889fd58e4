import pytest
import asyncio
from crawler.browser_search import crawl4ai_bing_search, crawl4ai_google_search
from urllib.parse import urlparse

# Helper function to validate URL
def is_valid_url(url):
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except (ValueError, AttributeError):
        return False

@pytest.mark.asyncio
async def test_crawl4ai_bing_search_basic(monkeypatch):
    """Tests basic functionality of Bing search using mock HTML."""
    # Mock the crawler to return a predefined HTML structure
    class MockCrawlResult:
        html = """
            <html><body>
            <ol id='b_results'>
                <li class='b_algo'>
                    <h2><a href='https://www.crawl4ai.com'>Crawl4AI Official Site</a></h2>
                    <div class="b_caption">
                        <p>The official website for Crawl4AI.</p>
                    </div>
                </li>
                <li class='b_algo'>
                    <h2><a href='https://github.com/crawl4ai'>Crawl4AI on GitHub</a></h2>
                    <div class="b_caption">
                        <p>The source code for Crawl4AI.</p>
                    </div>
                </li>
            </ol>
            </body></html>
        """

    async def mock_arun(*args, **kwargs):
        return MockCrawlResult()

    monkeypatch.setattr("crawler.browser_search.AsyncWebCrawler.arun", mock_arun)

    query = "a query that will be mocked"
    results = await crawl4ai_bing_search(query, max_results=5, lang="en")
    assert isinstance(results, list)
    assert len(results) == 2
    assert results[0]["title"] == "Crawl4AI Official Site"
    assert results[0]["url"] == "https://www.crawl4ai.com"
    assert results[0]["snippet"] == "The official website for Crawl4AI."

@pytest.mark.asyncio
async def test_crawl4ai_google_search_basic():
    """Tests basic functionality of Google search for a common query."""
    query = "Crawl4AI"
    results = await crawl4ai_google_search(query, max_results=5, lang="en")
    assert isinstance(results, list)
    assert len(results) > 0
    assert len(results) <= 5
    for res in results:
        assert "title" in res and isinstance(res["title"], str) and res["title"]
        assert "url" in res and isinstance(res["url"], str) and is_valid_url(res["url"])
        assert "snippet" in res and isinstance(res["snippet"], str)
    assert any("crawl4ai" in res["title"].lower() or "crawl4ai" in res["snippet"].lower() for res in results)

@pytest.mark.asyncio
async def test_crawl4ai_google_search_no_results(monkeypatch):
    """Tests Google search handles a page with no results."""
    # Mock the crawler to return an empty HTML body
    class MockCrawlResult:
        html = "<html><body></body></html>"

    async def mock_arun(*args, **kwargs):
        return MockCrawlResult()

    monkeypatch.setattr("crawler.browser_search.AsyncWebCrawler.arun", mock_arun)

    query = "a query that will be mocked"
    results = await crawl4ai_google_search(query, max_results=5, lang="en")
    assert isinstance(results, list)
    assert len(results) == 0

@pytest.mark.asyncio
async def test_crawl4ai_bing_search_no_results(monkeypatch):
    """Tests Bing search handles a page with no results."""
    # Mock the crawler to return an empty HTML body
    class MockCrawlResult:
        html = "<html><body></body></html>"

    async def mock_arun(*args, **kwargs):
        return MockCrawlResult()

    monkeypatch.setattr("crawler.browser_search.AsyncWebCrawler.arun", mock_arun)

    query = "a query that will be mocked"
    results = await crawl4ai_bing_search(query, max_results=5, lang="en")
    assert isinstance(results, list)
    assert len(results) == 0

@pytest.mark.asyncio
async def test_crawl4ai_google_search_max_results():
    """Tests that max_results parameter is respected in Google search."""
    query = "python"
    results = await crawl4ai_google_search(query, max_results=3, lang="en")
    assert isinstance(results, list)
    assert len(results) <= 3

@pytest.mark.asyncio
async def test_crawl4ai_bing_search_max_results(monkeypatch):
    """Tests that max_results is respected in Bing search using mock HTML."""
    # Mock the crawler to return a predefined HTML structure
    class MockCrawlResult:
        html = """
            <html><body>
            <ol id='b_results'>
                <li class='b_algo'><h2><a href='#'>1</a></h2></li>
                <li class='b_algo'><h2><a href='#'>2</a></h2></li>
                <li class='b_algo'><h2><a href='#'>3</a></h2></li>
                <li class='b_algo'><h2><a href='#'>4</a></h2></li>
            </ol>
            </body></html>
        """

    async def mock_arun(*args, **kwargs):
        return MockCrawlResult()

    monkeypatch.setattr("crawler.browser_search.AsyncWebCrawler.arun", mock_arun)

    query = "a query that will be mocked"
    results = await crawl4ai_bing_search(query, max_results=3, lang="en")
    assert isinstance(results, list)
    assert len(results) == 3
