"""
Comprehensive tests for company data extractors.

This module tests the new structured company data extraction system,
including regex and LLM extractors, schema validation, and fallback logic.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from typing import Dict, Any

from crawler.models import CompanyData, LegalStructure, Country
from crawler.extractors import (
    RegexCompanyDataExtractor,
    LLMCompanyDataExtractor,
    ExtractorFactory,
    ExtractorType,
    get_factory
)
from crawler.company_data import extract_company_data, extract_multiple_company_data


class TestCompanyDataModel:
    """Test the CompanyData Pydantic model."""

    def test_empty_company_data(self):
        """Test creating empty CompanyData instance."""
        data = CompanyData()
        assert data.company_name == ""
        assert data.legal_structure == LegalStructure.UNKNOWN
        assert data.confidence_score == 0.0
        assert not data.is_valid_extraction()

    def test_valid_company_data(self):
        """Test creating valid CompanyData instance."""
        data = CompanyData(
            company_name="Test GmbH",
            legal_structure=LegalStructure.GMBH,
            street_address="Teststraße 123",
            city="Berlin",
            email="<EMAIL>",
            confidence_score=0.8
        )
        assert data.company_name == "Test GmbH"
        assert data.legal_structure == LegalStructure.GMBH
        assert data.is_valid_extraction()

    def test_email_validation(self):
        """Test email validation in CompanyData."""
        # Valid email
        data = CompanyData(email="<EMAIL>")
        assert data.email == "<EMAIL>"

        # Invalid email
        data = CompanyData(email="invalid-email")
        assert data.email == ""

    def test_website_validation(self):
        """Test website URL validation."""
        # Valid URL
        data = CompanyData(website="https://example.com")
        assert data.website == "https://example.com"

        # URL without protocol (should be fixed)
        data = CompanyData(website="example.com")
        assert data.website == "https://example.com"

        # Invalid URL
        data = CompanyData(website="not-a-url")
        assert data.website == ""

    def test_get_summary(self):
        """Test the get_summary method."""
        data = CompanyData(
            company_name="Test GmbH",
            legal_structure=LegalStructure.GMBH,
            street_address="Teststraße 123",
            city="Berlin",
            email="<EMAIL>",
            phone="+49 30 12345678"
        )
        summary = data.get_summary()
        assert "Test GmbH (GmbH)" in summary
        assert "Teststraße 123" in summary
        assert "Berlin" in summary
        assert "<EMAIL>" in summary
        assert "+49 30 12345678" in summary


class TestRegexExtractor:
    """Test the regex-based company data extractor."""

    @pytest.fixture
    def extractor(self):
        """Create a regex extractor instance."""
        return RegexCompanyDataExtractor()

    @pytest.fixture
    def sample_impressum_html(self):
        """Sample German impressum HTML content."""
        return """
        <html>
        <body>
        <h1>Impressum</h1>
        <p>
        Musterfirma GmbH<br>
        Geschäftsführer: Max Mustermann<br>
        Musterstraße 123<br>
        12345 Berlin<br>
        Deutschland<br>
        <br>
        Telefon: +49 30 12345678<br>
        E-Mail: <EMAIL><br>
        Website: https://www.musterfirma.de<br>
        <br>
        Handelsregister: HRB 12345<br>
        Registergericht: Amtsgericht Berlin<br>
        Steuernummer: 123/456/78901<br>
        USt-IdNr.: DE123456789<br>
        </p>
        </body>
        </html>
        """

    @pytest.mark.asyncio
    async def test_regex_extraction_success(self, extractor, sample_impressum_html):
        """Test successful regex extraction."""
        result = await extractor.extract(sample_impressum_html, "https://example.com")

        assert result.success
        assert result.confidence_score > 0.5
        assert result.extraction_method == "regex"

        # Check extracted data
        data = result.data
        assert "Musterfirma GmbH" in data.get("company_name", "")
        assert data.get("legal_structure") == LegalStructure.GMBH
        assert "Musterstraße 123" in data.get("street_address", "")
        assert data.get("zip_code") == "12345"
        assert data.get("city") == "Berlin"
        assert data.get("email") == "<EMAIL>"
        assert "+49 30 12345678" in data.get("phone", "")
        assert "Max Mustermann" in data.get("managing_director", "")

    @pytest.mark.asyncio
    async def test_regex_extraction_empty_content(self, extractor):
        """Test regex extraction with empty content."""
        result = await extractor.extract("", "https://example.com")

        assert not result.success
        assert result.confidence_score == 0.0
        assert "No text content found" in result.error_message

    @pytest.mark.asyncio
    async def test_regex_extraction_minimal_data(self, extractor):
        """Test regex extraction with minimal data."""
        html = "<html><body><p>Test Company<br><EMAIL></p></body></html>"
        result = await extractor.extract(html, "https://example.com")

        # Should extract at least some data
        assert result.confidence_score > 0.0
        data = result.data
        assert data.get("email") == "<EMAIL>"


class TestLLMExtractor:
    """Test the LLM-based company data extractor."""

    @pytest.fixture
    def extractor(self):
        """Create an LLM extractor instance."""
        return LLMCompanyDataExtractor(model="test-model")

    @pytest.fixture
    def mock_llm_response(self):
        """Mock LLM response for testing."""
        return {
            'response': '''
            {
                "company_name": "AI Test GmbH",
                "legal_structure": "GmbH",
                "street_address": "AI Straße 456",
                "zip_code": "54321",
                "city": "Munich",
                "country": "Germany",
                "phone": "+49 89 87654321",
                "email": "<EMAIL>",
                "website": "https://ai-test.de",
                "managing_director": "Anna AI",
                "management_title": "Geschäftsführer",
                "registration_number": "HRB 54321",
                "register_court": "Amtsgericht München",
                "tax_id": "987/654/32109",
                "vat_id": "DE987654321"
            }
            '''
        }

    @pytest.mark.asyncio
    async def test_llm_extraction_success(self, extractor, mock_llm_response):
        """Test successful LLM extraction."""
        with patch.object(extractor.llm_client, 'generate', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_llm_response

            html = "<html><body><h1>Impressum</h1><p>Some complex impressum content</p></body></html>"
            result = await extractor.extract(html, "https://example.com")

            assert result.success
            assert result.confidence_score > 0.5
            assert result.extraction_method == "llm"

            # Check extracted data
            data = result.data
            assert data.get("company_name") == "AI Test GmbH"
            assert data.get("legal_structure") == "GmbH"
            assert data.get("email") == "<EMAIL>"
            assert data.get("city") == "Munich"

    @pytest.mark.asyncio
    async def test_llm_extraction_invalid_json(self, extractor):
        """Test LLM extraction with invalid JSON response."""
        with patch.object(extractor.llm_client, 'generate', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = {'response': 'Invalid JSON response'}

            html = "<html><body><p>Test content</p></body></html>"
            result = await extractor.extract(html, "https://example.com")

            assert not result.success
            assert "LLM extraction failed" in result.error_message

    @pytest.mark.asyncio
    async def test_llm_extraction_llm_error(self, extractor):
        """Test LLM extraction when LLM fails."""
        with patch.object(extractor.llm_client, 'generate', new_callable=AsyncMock) as mock_generate:
            mock_generate.side_effect = Exception("LLM connection failed")

            html = "<html><body><p>Test content</p></body></html>"
            result = await extractor.extract(html, "https://example.com")

            assert not result.success
            assert "LLM extraction failed" in result.error_message


class TestExtractorFactory:
    """Test the extractor factory and fallback logic."""

    @pytest.fixture
    def factory(self):
        """Create a factory instance."""
        return ExtractorFactory()

    def test_create_regex_extractor(self, factory):
        """Test creating regex extractor."""
        extractor = factory.create_extractor(ExtractorType.REGEX)
        assert isinstance(extractor, RegexCompanyDataExtractor)
        assert extractor.name == "regex"

    def test_create_llm_extractor(self, factory):
        """Test creating LLM extractor."""
        extractor = factory.create_extractor(
            ExtractorType.LLM, model="test-model")
        assert isinstance(extractor, LLMCompanyDataExtractor)
        assert extractor.name == "llm"

    def test_get_or_create_extractor_caching(self, factory):
        """Test extractor caching."""
        extractor1 = factory.get_or_create_extractor(ExtractorType.REGEX)
        extractor2 = factory.get_or_create_extractor(ExtractorType.REGEX)
        assert extractor1 is extractor2  # Should be the same instance

    @pytest.mark.asyncio
    async def test_extract_with_fallback_success_primary(self, factory):
        """Test fallback extraction when primary succeeds."""
        html = "<html><body><p>Test GmbH<br><EMAIL></p></body></html>"

        with patch.object(RegexCompanyDataExtractor, 'extract', new_callable=AsyncMock) as mock_extract:
            # Mock successful primary extraction
            mock_result = MagicMock()
            mock_result.success = True
            mock_result.confidence_score = 0.8
            mock_result.data = {"company_name": "Test GmbH",
                                "email": "<EMAIL>"}
            mock_extract.return_value = mock_result

            result = await factory.extract_with_fallback(
                html_content=html,
                source_url="https://example.com",
                primary_type=ExtractorType.REGEX,
                fallback_type=ExtractorType.LLM
            )

            assert result.success
            assert result.confidence_score == 0.8

    @pytest.mark.asyncio
    async def test_extract_with_fallback_primary_fails(self, factory):
        """Test fallback extraction when primary fails."""
        html = "<html><body><p>Complex content</p></body></html>"

        with patch.object(RegexCompanyDataExtractor, 'extract', new_callable=AsyncMock) as mock_regex:
            with patch.object(LLMCompanyDataExtractor, 'extract', new_callable=AsyncMock) as mock_llm:
                # Mock failed primary extraction
                mock_regex_result = MagicMock()
                mock_regex_result.success = False
                mock_regex_result.confidence_score = 0.1
                mock_regex.return_value = mock_regex_result

                # Mock successful fallback extraction
                mock_llm_result = MagicMock()
                mock_llm_result.success = True
                mock_llm_result.confidence_score = 0.7
                mock_llm.return_value = mock_llm_result

                result = await factory.extract_with_fallback(
                    html_content=html,
                    source_url="https://example.com",
                    primary_type=ExtractorType.REGEX,
                    fallback_type=ExtractorType.LLM
                )

                assert result.success
                assert result.confidence_score == 0.7


class TestIntegrationFunctions:
    """Test the main integration functions."""

    @pytest.mark.asyncio
    async def test_extract_company_data_success(self):
        """Test successful company data extraction."""
        with patch('crawler.company_data.crawl_impressum', new_callable=AsyncMock) as mock_crawl:
            with patch('builtins.open', create=True) as mock_open:
                with patch('crawler.extractors.get_factory') as mock_get_factory:
                    # Mock crawl_impressum response
                    mock_crawl.return_value = (
                        "Impressum content", "/path/to/file.html")

                    # Mock file reading
                    mock_open.return_value.__enter__.return_value.read.return_value = "<html>content</html>"

                    # Mock factory and extraction
                    mock_factory = MagicMock()
                    mock_result = MagicMock()
                    mock_result.to_company_data.return_value = CompanyData(
                        company_name="Test Company",
                        confidence_score=0.8
                    )
                    mock_factory.extract_with_auto_selection.return_value = mock_result
                    mock_get_factory.return_value = mock_factory

                    # Test extraction
                    result = await extract_company_data("https://example.com")

                    assert isinstance(result, CompanyData)
                    assert result.company_name == "Test Company"
                    assert result.confidence_score == 0.8

    @pytest.mark.asyncio
    async def test_extract_company_data_no_impressum(self):
        """Test company data extraction when no impressum found."""
        with patch('crawler.company_data.crawl_impressum', new_callable=AsyncMock) as mock_crawl:
            # Mock no impressum found
            mock_crawl.return_value = (None, None)

            result = await extract_company_data("https://example.com")

            assert isinstance(result, CompanyData)
            assert result.confidence_score == 0.0
            assert result.extraction_method == "failed"

    @pytest.mark.asyncio
    async def test_extract_multiple_company_data(self):
        """Test extracting company data from multiple URLs."""
        urls = ["https://example1.com", "https://example2.com"]

        with patch('crawler.company_data.extract_company_data', new_callable=AsyncMock) as mock_extract:
            # Mock successful extractions
            mock_extract.side_effect = [
                CompanyData(company_name="Company 1", confidence_score=0.8),
                CompanyData(company_name="Company 2", confidence_score=0.7)
            ]

            results = await extract_multiple_company_data(urls)

            assert len(results) == 2
            assert results[0].company_name == "Company 1"
            assert results[1].company_name == "Company 2"
            assert mock_extract.call_count == 2


class TestEdgeCases:
    """Test edge cases and error conditions."""

    @pytest.mark.asyncio
    async def test_malformed_html(self):
        """Test extraction with malformed HTML."""
        extractor = RegexCompanyDataExtractor()
        malformed_html = "<html><body><p>Unclosed tag<br>Test GmbH</body>"

        result = await extractor.extract(malformed_html, "https://example.com")

        # Should still work with malformed HTML
        assert result.processing_time_ms > 0
        # May or may not succeed depending on content

    @pytest.mark.asyncio
    async def test_very_large_content(self):
        """Test extraction with very large content."""
        extractor = LLMCompanyDataExtractor()
        large_html = "<html><body>" + "x" * 10000 + "</body></html>"

        with patch.object(extractor.llm_client, 'generate', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = {
                'response': '{"company_name": "Test"}'}

            result = await extractor.extract(large_html, "https://example.com")

            # Should handle large content by truncating
            assert result.raw_content_length == len(large_html)
            assert result.processed_content_length <= 4000

    def test_global_factory_singleton(self):
        """Test that get_factory returns singleton."""
        factory1 = get_factory()
        factory2 = get_factory()
        assert factory1 is factory2

    @pytest.mark.asyncio
    async def test_concurrent_extractions(self):
        """Test concurrent extractions don't interfere."""
        factory = ExtractorFactory()
        html = "<html><body><p>Test GmbH<br><EMAIL></p></body></html>"

        # Run multiple extractions concurrently
        tasks = [
            factory.extract_with_fallback(
                html, f"https://example{i}.com", ExtractorType.REGEX, ExtractorType.LLM)
            for i in range(5)
        ]

        results = await asyncio.gather(*tasks)

        # All should complete
        assert len(results) == 5
        for result in results:
            assert result.processing_time_ms > 0


if __name__ == "__main__":
    pytest.main([__file__])
