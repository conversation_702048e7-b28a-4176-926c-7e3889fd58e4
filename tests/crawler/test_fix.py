#!/usr/bin/env python3
"""
Test script to verify the multiple URL impressum crawling fix.
"""

import asyncio
import sys
from agent.langgraph_workflow import create_workflow
from agent.langgraph_state import create_initial_state


async def test_multiple_impressum_fix():
    """Test the fix for multiple impressum crawling."""
    print("🧪 Testing Multiple URL Impressum Crawling Fix")
    print("=" * 50)

    try:
        # Test 1: Workflow compilation
        print("1. Testing workflow compilation...")
        workflow = create_workflow()
        app = workflow.compile()
        print("   ✅ Workflow compiled successfully")

        # Test 2: Multiple impressum command parsing
        print("\n2. Testing multiple impressum command...")
        command = "crawl imprint from udobaer.de, udobaer.at"
        print(f"   Command: {command}")

        initial_state = create_initial_state(command)
        print("   ✅ Initial state created successfully")

        # Test 3: Workflow execution (with timeout)
        print("\n3. Testing workflow execution...")
        try:
            result = await asyncio.wait_for(app.ainvoke(initial_state), timeout=60.0)

            if 'error' in result and result['error']:
                print(f"   ❌ Workflow error: {result['error']}")
                return False
            else:
                print("   ✅ Workflow executed without NoneType error")

                # Check for expected result structure
                if 'result' in result:
                    res = result['result']
                    if res and 'multiple_impressum_results' in res:
                        impressum_results = res['multiple_impressum_results']
                        print(
                            f"   📊 Found {len(impressum_results)} impressum results")

                        for i, impressum_result in enumerate(impressum_results, 1):
                            status = "✅" if impressum_result.get(
                                'success') else "❌"
                            url = impressum_result.get('url', 'Unknown')
                            print(f"      {i}. {status} {url}")
                    else:
                        print("   ⚠️  No multiple_impressum_results found in result")
                        if res:
                            print(f"   📋 Available keys: {list(res.keys())}")
                        else:
                            print("   📋 Result is None")
                else:
                    print("   ⚠️  No result key found")
                    print(f"   📋 Available keys: {list(result.keys())}")

                return True

        except asyncio.TimeoutError:
            print("   ⏰ Workflow execution timed out (but no NoneType error)")
            return True  # Timeout is OK, we just want to avoid the NoneType error
        except Exception as e:
            if "argument of type 'NoneType' is not iterable" in str(e):
                print(f"   ❌ NoneType error still occurs: {e}")
                print("   📍 Full traceback:")
                import traceback
                traceback.print_exc()
                return False
            else:
                print(f"   ⚠️  Other error (not NoneType): {e}")
                return True  # Other errors are OK for this test

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_result_formatter_fix():
    """Test the specific result formatter fix."""
    print("\n🧪 Testing Result Formatter Fix")
    print("=" * 30)

    try:
        from agent.langgraph_nodes import result_formatter_node
        from agent.langgraph_state import CrawlAgentState

        # Test with None parsed_intent
        test_state = {
            "parsed_intent": None,  # This was causing the error
            "summary": "Test summary",
            "next_action": "format_output"
        }

        print("1. Testing with None parsed_intent...")
        result = await result_formatter_node(test_state)

        if 'error' in result:
            print(f"   ❌ Error: {result['error']}")
            return False
        else:
            print("   ✅ No NoneType error with None parsed_intent")

        # Test with empty parsed_intent
        test_state["parsed_intent"] = {}
        print("2. Testing with empty parsed_intent...")
        result = await result_formatter_node(test_state)

        if 'error' in result:
            print(f"   ❌ Error: {result['error']}")
            return False
        else:
            print("   ✅ No error with empty parsed_intent")

        return True

    except Exception as e:
        print(f"❌ Result formatter test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Multiple URL Impressum Crawling Fix Verification")
    print("=" * 60)

    # Test 1: Result formatter fix
    formatter_ok = await test_result_formatter_fix()

    # Test 2: Full workflow fix
    workflow_ok = await test_multiple_impressum_fix()

    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Result Formatter Fix: {'✅ PASS' if formatter_ok else '❌ FAIL'}")
    print(f"   Workflow Execution:   {'✅ PASS' if workflow_ok else '❌ FAIL'}")

    if formatter_ok and workflow_ok:
        print("\n🎉 All tests passed! The NoneType error fix is working.")
        return 0
    else:
        print("\n❌ Some tests failed. The fix needs more work.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
