#!/usr/bin/env python3
"""
Test script to verify all LLM providers (Ollama, OpenAI, Groq) are working correctly.
"""

import asyncio
import sys
from agent.llm.client import get_chat_response_from_llm, get_summary_from_llm
from agent.llm.llm_config import LLMConfig


async def test_provider(provider_name, model_name):
    """Test a specific provider with a simple chat request."""
    print(f"\n🧪 Testing {provider_name.upper()} with model: {model_name}")
    print("=" * 60)
    
    try:
        # Test simple chat
        print("📝 Testing chat response...")
        response = await get_chat_response_from_llm(
            "Hello! Please respond with exactly: 'Provider test successful'"
        )
        print(f"✅ Chat Response: {response}")
        
        # Test summarization
        print("\n📄 Testing summarization...")
        summary = await get_summary_from_llm(
            "This is a test document about artificial intelligence and machine learning. "
            "AI has revolutionized many industries and continues to grow rapidly."
        )
        print(f"✅ Summary: {summary}")
        
        print(f"🎉 {provider_name.upper()} test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ {provider_name.upper()} test failed: {str(e)}")
        return False


async def test_all_providers():
    """Test all configured providers."""
    print("🚀 Multi-Provider LLM Testing")
    print("=" * 60)
    
    # Test configurations for each provider
    test_configs = [
        ("ollama", "qwen3"),
        ("openai", "gpt-4o-mini"),
        ("groq", "llama-3.1-8b-instant")
    ]
    
    results = {}
    
    for provider, model in test_configs:
        print(f"\n🔄 Switching to {provider}...")
        
        # Temporarily update config for testing
        original_config = LLMConfig.create_from_ini()
        
        # Create test config
        test_config = LLMConfig(
            provider=provider,
            primary_model=model,
            fallback_models=[]
        )
        
        # Update the singleton instance for testing
        LLMConfig._instance = test_config
        
        try:
            success = await test_provider(provider, model)
            results[provider] = success
        except Exception as e:
            print(f"❌ Failed to test {provider}: {str(e)}")
            results[provider] = False
        
        # Restore original config
        LLMConfig._instance = original_config
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    for provider, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{provider.upper():10} | {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\nResults: {total_passed}/{total_tests} providers working")
    
    if total_passed == total_tests:
        print("🎉 All providers are working correctly!")
    elif total_passed > 0:
        print("⚠️  Some providers are working. Check failed providers above.")
    else:
        print("❌ No providers are working. Check your configuration.")
    
    return results


async def test_current_provider():
    """Test the currently configured provider."""
    print("🔍 Testing Current Provider Configuration")
    print("=" * 60)
    
    config = LLMConfig.create_from_ini()
    print(f"Current Provider: {config.provider}")
    print(f"Current Model: {config.primary_model}")
    
    try:
        response = await get_chat_response_from_llm("Hello! Please say 'Current provider test successful'")
        print(f"✅ Response: {response}")
        return True
    except Exception as e:
        print(f"❌ Current provider test failed: {str(e)}")
        return False


if __name__ == "__main__":
    print("🧪 LiteLLM Multi-Provider Test Suite")
    print("=" * 60)
    
    # Test current provider first
    print("\n1️⃣ Testing current configuration...")
    current_success = asyncio.run(test_current_provider())
    
    # Test all providers
    print("\n2️⃣ Testing all providers...")
    results = asyncio.run(test_all_providers())
    
    # Final summary
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    if current_success:
        print("✅ Current configuration is working")
    else:
        print("❌ Current configuration has issues")
    
    working_providers = [p for p, success in results.items() if success]
    if working_providers:
        print(f"✅ Working providers: {', '.join(working_providers)}")
        print("\n💡 You can switch providers by updating the 'provider' setting in config.ini")
    else:
        print("❌ No providers are working - check your API keys and configuration")
