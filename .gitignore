# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
.env/
.venv/
env/
venv/

# VSCode / PyCharm settings
.vscode/
.idea/

# Distribution / packaging
build/
dist/
*.egg-info/

# Logs
*.log
logs/

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.cache
.pytest_cache/

# SQLite
*.db-shm
*.db-wal

# Generated artifacts
graph

# Environment variables and secrets
.env
.env.local
*.env

# Ollama or embedding cache (if applicable)
ollama_cache/
chroma_index/
scraped_html/
vector_store/
*.db

# Temporary files
test_playwright_minimal.py

# Local data files
data/
output/
results/
examples/
*.json
*.sqlite

# Media files generated by crawler
screenshots/
pdfs/

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# macOS and system files
.DS_Store
Thumbs.db
