"""
LangGraph State Schema for the Crawl4AI Agent

This module defines the TypedDict state schema that holds intermediate data
throughout the agent workflow, including user intent, URLs, HTML content,
extracted data, and results.
"""

from typing import TypedDict, Optional, Dict, Any, List, Type
from typing_extensions import Annotated
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage
from pydantic import BaseModel


class CrawlAgentState(TypedDict):
    """
    State schema for the Crawl4AI LangGraph agent.

    This state is passed between nodes in the workflow and accumulates
    data as the agent processes user requests.
    """

    # User input and intent
    user_prompt: str
    """The original user prompt/instruction"""

    parsed_intent: Optional[Dict[str, Any]]
    """Parsed user intent with command and parameters"""

    # Target information
    url: Optional[str]
    """Target URL to crawl (if applicable)"""

    urls: Optional[List[str]]
    """Multiple URLs to crawl (if applicable)"""

    local_file_path: Optional[str]
    """Local file path to process (if applicable)"""

    search_query: Optional[str]
    """Search query for web search (if applicable)"""

    # Crawling options
    screenshot: bool
    """Whether to capture screenshots"""

    pdf: bool
    """Whether to generate PDFs"""

    # Content and data
    html_content: Optional[str]
    """Raw HTML content from crawling"""

    markdown_content: Optional[str]
    """Markdown-formatted content"""

    extracted_data: Optional[Dict[str, Any]]
    """Structured data extracted from content"""

    # File paths
    saved_html_path: Optional[str]
    """Path to saved HTML file"""

    screenshot_path: Optional[str]
    """Path to saved screenshot"""

    pdf_path: Optional[str]
    """Path to saved PDF"""

    summary_path: Optional[str]
    """Path to saved summary file"""

    json_path: Optional[str]
    """Path to saved JSON data file"""

    links_file_path: Optional[str]
    """Path to saved links JSON file"""

    # Link extraction data
    page_title: Optional[str]
    """Title of the crawled page"""

    links: Optional[Dict[str, List[Dict[str, str]]]]
    """Extracted links categorized as internal/external with titles"""

    link_summary: Optional[Dict[str, int]]
    """Summary statistics of extracted links"""

    # Processing results
    summary: Optional[str]
    """Generated summary of content"""

    company_data: Optional[Dict[str, Any]]
    """Extracted company/legal data"""

    search_results: Optional[List[Dict[str, Any]]]
    """Web search results"""

    crawled_search_results: Optional[List[Dict[str, Any]]]
    """Crawled content from search results"""

    # Schema learning fields
    target_model: Optional[Type[BaseModel]]
    """Pydantic model for schema learning extraction"""

    schema_provider: Optional[str]
    """LLM provider for schema learning"""

    schema_api_token: Optional[str]
    """API token for schema learning LLM"""

    force_learn_schema: bool
    """Force learning new schema even if cached one exists"""

    schema_dir: Optional[str]
    """Directory to store learned schemas"""

    learned_schema: Optional[Dict[str, Any]]
    """The learned or cached schema"""

    schema_extracted_data: Optional[List[Dict[str, Any]]]
    """Data extracted using schema learning"""

    # Messages for LLM interactions
    messages: Annotated[List[BaseMessage], add_messages]
    """Chat messages for LLM interactions"""

    # Error handling
    error: Optional[str]
    """Error message if something goes wrong"""

    # Multiple URL results
    multiple_crawl_results: Optional[List[Dict[str, Any]]]
    """Results from crawling multiple websites"""

    successful_crawls: Optional[int]
    """Number of successful crawls in multiple website operation"""

    failed_crawls: Optional[int]
    """Number of failed crawls in multiple website operation"""

    html_download_results: Optional[List[Dict[str, Any]]]
    """Results from downloading HTML from multiple websites"""

    successful_downloads: Optional[int]
    """Number of successful HTML downloads in multiple download operation"""

    failed_downloads: Optional[int]
    """Number of failed HTML downloads in multiple download operation"""

    # Summary generation results
    successful_summaries: Optional[int]
    """Number of successful summaries generated"""

    failed_summaries: Optional[int]
    """Number of failed summaries in multiple summary generation"""

    # Workflow control
    next_action: Optional[str]
    """Next action to take in the workflow"""

    # Results
    result: Optional[Dict[str, Any]]
    """Final result to return to user"""


class WorkflowCommands:
    """Constants for workflow commands and actions"""

    # Main commands
    CRAWL_WEBSITE = "crawl_website"
    CRAWL_IMPRESSUM = "crawl_impressum"
    PROCESS_LOCAL_FILE = "process_local_file"
    EXTRACT_LEGAL_INFO = "extract_legal_info"
    SUMMARIZE = "summarize"
    WEB_SEARCH = "web_search"
    SEARCH_AND_CRAWL = "search_and_crawl"
    CRAWL_WITH_LINKS = "crawl_with_links"
    CRAWL_MULTIPLE_WEBSITES = "crawl_multiple_websites"
    CRAWL_MULTIPLE_IMPRESSUMS = "crawl_multiple_impressums"
    SMART_CRAWL = "smart_crawl"
    SITEMAP_EXTRACTOR = "sitemap_extractor"
    SITEMAP_MULTIPLE = "sitemap_multiple"
    DOWNLOAD_HTML = "download_html"
    DOWNLOAD_MULTIPLE_HTML = "download_multiple_html"
    CHAT = "chat"

    # Statistics commands
    STATISTICS_SUMMARY = "statistics_summary"
    DOMAIN_ANALYSIS = "domain_analysis"
    RECENT_ACTIVITY = "recent_activity"
    PREVIEW_HISTORICAL_IMPORT = "preview_historical_import"
    IMPORT_HISTORICAL_DATA = "import_historical_data"

    # Workflow actions
    PARSE_INTENT = "parse_intent"
    VALIDATE_INPUT = "validate_input"
    CRAWL_URL = "crawl_url"
    FIND_IMPRESSUM = "find_impressum"
    EXTRACT_DATA = "extract_data"
    GENERATE_SUMMARY = "generate_summary"
    GENERATE_MULTIPLE_SUMMARIES = "generate_multiple_summaries"
    SAVE_RESULTS = "save_results"
    FORMAT_OUTPUT = "format_output"

    # Node names
    INTENT_PARSER = "intent_parser"
    INPUT_VALIDATOR = "input_validator"
    URL_CRAWLER = "url_crawler"
    LOCAL_FILE_PROCESSOR = "local_file_processor"
    LINK_EXTRACTOR = "link_extractor"
    IMPRESSUM_FINDER = "impressum_finder"
    DATA_EXTRACTOR = "data_extractor"
    SUMMARIZER = "summarizer"
    WEB_SEARCHER = "web_searcher"
    SEARCH_AND_CRAWLER = "search_and_crawler"
    SMART_CRAWLER = "smart_crawler"
    RESULT_FORMATTER = "result_formatter"

    # End states
    END = "__end__"
    ERROR = "__error__"


def create_initial_state(user_prompt: str) -> CrawlAgentState:
    """
    Create an initial state for the agent workflow.

    Args:
        user_prompt: The user's input prompt

    Returns:
        Initial CrawlAgentState with user prompt and default values
    """
    return CrawlAgentState(
        user_prompt=user_prompt,
        parsed_intent=None,
        url=None,
        urls=None,
        local_file_path=None,
        search_query=None,
        screenshot=False,
        pdf=False,
        html_content=None,
        markdown_content=None,
        extracted_data=None,
        saved_html_path=None,
        screenshot_path=None,
        pdf_path=None,
        summary_path=None,
        json_path=None,
        links_file_path=None,
        page_title=None,
        links=None,
        link_summary=None,
        summary=None,
        company_data=None,
        search_results=None,
        crawled_search_results=None,
        target_model=None,
        schema_provider=None,
        schema_api_token=None,
        force_learn_schema=False,
        schema_dir=None,
        learned_schema=None,
        schema_extracted_data=None,
        messages=[],
        error=None,
        next_action=None,
        result=None
    )
