"""
High-level client for interacting with LLM services via LiteLLM.
Maintains backward compatibility with the original Ollama client signatures.
"""

from typing import Dict, Any
from .llm_factory import get_default_llm_factory
from .exceptions import LLMError
from utils.logger import logger

async def get_summary_from_llm(content: str, model: str = None) -> str:
    try:
        factory = get_default_llm_factory()
        summary_service = factory.get_summary_service()
        return await summary_service.generate_summary(content, model)
    except LLMError as e:
        logger.error(f"Summary generation failed: {e}")
        return f"Error generating summary: {e}"
    except Exception as e:
        logger.error(f"Unexpected error in summary generation: {e}")
        return f"An unexpected error occurred during summarization: {e}"

async def get_chat_response_from_llm(prompt: str, model: str = None) -> str:
    try:
        factory = get_default_llm_factory()
        chat_service = factory.get_chat_service()
        return await chat_service.get_chat_response(prompt, model)
    except LLMError as e:
        logger.error(f"Chat response generation failed: {e}")
        return f"Error communicating with LLM: {e}"
    except Exception as e:
        logger.error(f"Unexpected error in chat response: {e}")
        return f"An unexpected error occurred: {str(e)}"

async def parse_command_from_prompt_llm(user_prompt: str, model: str = None) -> Dict[str, Any]:
    try:
        factory = get_default_llm_factory()
        parsing_service = factory.get_command_parsing_service()
        return await parsing_service.parse_command(user_prompt, model)
    except Exception as e:
        logger.error(f"Command parsing failed: {e}")
        return {"command": "chat", "url": None, "error": str(e)}

async def extract_company_data_from_impressum_llm(content: str, url: str, model: str = None) -> Dict[str, Any]:
    try:
        factory = get_default_llm_factory()
        extraction_service = factory.get_company_data_service()
        return await extraction_service.extract_company_data(content, url, model)
    except LLMError as e:
        logger.error(f"Company data extraction failed: {e}")
        return {"error": f"Error during data extraction: {str(e)}"}
    except Exception as e:
        logger.error(f"Unexpected error in company data extraction: {e}")
        return {"error": f"An unexpected error occurred: {str(e)}"}
