"""
Base client for LiteLLM services.
Provides common functionality for making API requests with retry logic,
timeout handling, and error management.
"""

import asyncio
import litellm
from typing import Dict, Any, Optional, List
from utils.logger import logger
from .llm_config import LLMConfig, RetryConfig
from .exceptions import (
    LLMConnectionError,
    LLMTimeoutError,
    LLMAPIError,
    LLMAuthenticationError,
    LLMResponseError,
)

class BaseLLMClient:
    """Base client for making requests via LiteLLM."""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """Initialize the base client with configuration."""
        self.config = config or LLMConfig.create_from_ini()

    async def _make_request(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.5,
        max_tokens: int = 1500,
        timeout: int = 60,
        format_json: bool = False,
        retry_config: Optional[RetryConfig] = None,
        operation_name: str = "request"
    ) -> Dict[str, Any]:
        """
        Make an API request using litellm with retry logic.
        """
        retry_config = retry_config or self.config.retry
        last_exception = None
        
        model_to_use = model or self.config.primary_model

        for attempt in range(retry_config.max_attempts):
            try:
                if self.config.debug_logging:
                    logger.info(f"Making {operation_name} request to {model_to_use} (attempt {attempt + 1}/{retry_config.max_attempts})")

                kwargs = {
                    "model": model_to_use,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "timeout": timeout,
                    "api_key": self.config.api_key,
                    "base_url": self.config.base_url
                }
                
                if format_json:
                    kwargs["response_format"] = {"type": "json_object"}

                response = await litellm.acompletion(**kwargs)
                
                if self.config.log_responses and self.config.debug_logging:
                    response_text = response.choices[0].message.content or ""
                    logger.debug(f"{operation_name} response: {response_text[:200]}...")
                
                return response

            except litellm.exceptions.Timeout as e:
                last_exception = LLMTimeoutError(f"{operation_name} request timed out: {e}")
            except litellm.exceptions.AuthenticationError as e:
                raise LLMAuthenticationError(f"Authentication failed: {e}")
            except litellm.exceptions.APIConnectionError as e:
                last_exception = LLMConnectionError(f"Connection error: {e}")
            except litellm.exceptions.APIError as e:
                last_exception = LLMAPIError(f"API error: {e}")
            except Exception as e:
                last_exception = LLMResponseError(f"An unexpected error occurred: {e}")

            if attempt < retry_config.max_attempts - 1:
                delay = min(
                    retry_config.base_delay * (retry_config.exponential_base ** attempt),
                    retry_config.max_delay
                )
                logger.warning(f"{operation_name} failed, retrying in {delay}s... ({last_exception})")
                await asyncio.sleep(delay)
            else:
                logger.error(f"{operation_name} failed after {retry_config.max_attempts} attempts.")
                raise last_exception
        
        raise LLMResponseError("Request failed after all retries")
