"""
Custom exceptions for the LiteLLM service.
"""

class LLMError(Exception):
    """Base exception for all LiteLLM service errors."""
    pass

class LLMConnectionError(LLMError):
    """Raised for connection-related issues."""
    pass

class LLMTimeoutError(LLMError):
    """Raised when a request times out."""
    pass

class LLMResponseError(LLMError):
    """Raised for invalid or unexpected responses from the API."""
    pass

class LLMModelNotFoundError(LLMError):
    """Raised when the specified model is not found or available."""
    pass

class LLMAuthenticationError(LLMError):
    """Raised for API key or authentication errors."""
    pass

class LLMAPIError(LLMError):
    """Raised for general API errors returned by the provider."""
    pass
