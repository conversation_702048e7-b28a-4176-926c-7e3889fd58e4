"""
Service for extracting structured company data using LiteLLM.
"""

import json
from typing import Optional, Dict, Any
from utils.logger import logger
from ..base_llm_client import BaseLLMClient
from ..llm_config import LLMConfig
from ..exceptions import LLMError

class CompanyDataExtractionService(BaseLLMClient):
    """Service for extracting structured company data."""

    def __init__(self, config: Optional[LLMConfig] = None):
        super().__init__(config)

    async def extract_company_data(self, content: str, url: str, model: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract structured company data from content.
        """
        try:
            prompt = self._build_extraction_prompt(content, url)
            
            messages = [{"role": "system", "content": "You are a data extraction assistant."},
                        {"role": "user", "content": prompt}]

            response = await self._make_request(
                messages=messages,
                model=model or self.config.default_models.company_data,
                timeout=self.config.timeouts.company_data,
                format_json=True,
                operation_name="company data extraction"
            )

            extracted_data = json.loads(response.choices[0].message.content)
            return extracted_data

        except (LLMError, json.JSONDecodeError) as e:
            logger.error(f"Company data extraction failed: {e}")
            return {"error": f"Data extraction failed: {str(e)}"}
        except Exception as e:
            logger.error(f"Unexpected error in data extraction: {e}")
            return {"error": f"An unexpected error occurred: {str(e)}"}

    def _build_extraction_prompt(self, content: str, url: str) -> str:
        return (
            f"Extract the company name, address, and contact details from the following text from {url}. "
            f"Respond with JSON only. Example: {{'name': 'Example Corp', 'address': '123 Main St', 'phone': '555-1234'}}\n\n"
            f"Content: '{content}'"
        )
