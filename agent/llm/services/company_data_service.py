"""
Service for extracting structured company data using LiteLLM.
"""

import json
from typing import Optional, Dict, Any
from utils.logger import logger
from ..base_llm_client import BaseLLMClient
from ..llm_config import LLMConfig
from ..exceptions import LLMError

class CompanyDataExtractionService(BaseLLMClient):
    """Service for extracting structured company data."""

    def __init__(self, config: Optional[LLMConfig] = None):
        super().__init__(config)

    async def extract_company_data(self, content: str, url: str, model: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract structured company data from content.
        """
        try:
            prompt = self._build_extraction_prompt(content, url)
            
            messages = [{"role": "system", "content": "You are a data extraction assistant."},
                        {"role": "user", "content": prompt}]

            response = await self._make_request(
                messages=messages,
                model=model or self.config.default_models.company_data,
                timeout=self.config.timeouts.company_data,
                format_json=True,
                operation_name="company data extraction"
            )

            extracted_data = json.loads(response.choices[0].message.content)
            return extracted_data

        except (LLMError, json.JSONDecodeError) as e:
            logger.error(f"Company data extraction failed: {e}")
            return {"error": f"Data extraction failed: {str(e)}"}
        except Exception as e:
            logger.error(f"Unexpected error in data extraction: {e}")
            return {"error": f"An unexpected error occurred: {str(e)}"}

    def _build_extraction_prompt(self, content: str, url: str) -> str:
        """Build comprehensive German-specific extraction prompt."""
        return f"""You are a specialized data extraction assistant. Your task is to extract company information from the German Impressum/Imprint content below.

Your response MUST be a single JSON object. Do not add any text before or after the JSON.

IMPORTANT RULES:
1. Extract ONLY explicitly stated information. Do not make assumptions or inferences.
2. If a value cannot be found in the content, use "Not available".
3. Preserve the exact original formatting of numbers, addresses, and names.
4. For addresses, only combine elements that appear together in the text.
5. Look for German terms like:
   - "Geschäftsführer", "Inhaber", "Vertreten durch", "Vertretungsberechtigter" (managing director/owner)
   - "Handelsregister", "Registergericht" (commercial register)
   - "Registernummer", "HRB", "HRA" (registration number)
   - "Steuernummer", "Steuer-Nr." (tax ID)
   - "USt-IdNr.", "Umsatzsteuer-ID", "USt-ID", "Ust-Identifikationsnummer" (VAT ID)
   - "GmbH", "AG", "e.K.", "KG", "OHG", "UG", "GbR" (legal structure)
   - "Anschrift", "Adresse", "Sitz" (address)
6. BE THOROUGH - scan the entire text carefully for each piece of information.
7. If you find a company name, even without a legal structure suffix, extract it.
8. For email addresses, look for @ symbols or text like "E-Mail:" followed by an address.
9. For phone numbers, look for patterns like "+49", "Tel:", "Telefon:", followed by numbers.
10. For websites, look for "www", "http", or "Website:" followed by a domain.

EXAMPLES:
- "Weber Büro GmbH" → company_name: "Weber Büro", legal_structure: "GmbH"
- "Inhaber: Max Mustermann" → managing_director: "Max Mustermann"
- "Handelsregister: Amtsgericht Berlin HRB 12345" → register_court: "Amtsgericht Berlin", registration_number: "HRB 12345"
- "USt-IdNr.: DE123456789" → vat_id: "DE123456789"

JSON structure to fill:
{{
    "company_name": "...",
    "legal_structure": "...",
    "street_address": "...",
    "zip_code": "...",
    "city": "...",
    "country": "...",
    "phone": "...",
    "email": "...",
    "website": "...",
    "managing_director": "...",
    "registration_number": "...",
    "register_court": "...",
    "tax_id": "...",
    "vat_id": "..."
}}

CONTENT TO PROCESS (from {url}):
---
{content}
---"""
