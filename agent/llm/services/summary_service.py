"""
Summary service for generating content summaries using LiteLLM.
"""

from typing import Optional
from utils.logger import logger
from ..base_llm_client import BaseLLMClient
from ..llm_config import LLMConfig
from ..exceptions import LLMError

class SummaryService(BaseLLMClient):
    """Service for generating content summaries."""

    def __init__(self, config: Optional[LLMConfig] = None):
        super().__init__(config)

    async def generate_summary(
        self,
        content: str,
        model: Optional[str] = None,
        focus: str = "business activity"
    ) -> str:
        """
        Generate a concise summary of website content.
        """
        try:
            if not content or not content.strip():
                return "No content provided for summarization."

            processed_content = self._preprocess_content(content)
            prompt = self._build_summary_prompt(processed_content, focus)
            
            messages = [{"role": "user", "content": prompt}]

            response = await self._make_request(
                messages=messages,
                model=model or self.config.default_models.summary,
                timeout=self.config.timeouts.summary,
                operation_name="summary generation"
            )

            summary = response.choices[0].message.content.strip()

            if not summary:
                logger.warning("LLM returned empty summary")
                return "Unable to generate summary - empty response from model."
            
            logger.info(f"Generated summary of {len(summary)} characters")
            return summary

        except LLMError as e:
            logger.error(f"Summary generation failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in summary generation: {e}")
            raise LLMError(f"Summary generation failed: {str(e)}")

    def _preprocess_content(self, content: str) -> str:
        content = ' '.join(content.split())
        if len(content) > self.config.max_content_length:
            logger.info(f"Truncating content from {len(content)} to {self.config.max_content_length} characters")
            content = content[:self.config.max_content_length] + "..."
        return content

    def _build_summary_prompt(self, content: str, focus: str) -> str:
        return (
            f"Please give me a concise summary of the following website content "
            f"with focus on {focus}:\n\n"
            f"{content}\n\n"
            f"Summary:"
        )
