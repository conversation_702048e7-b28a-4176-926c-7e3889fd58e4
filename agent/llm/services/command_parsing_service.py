"""
Service for parsing user commands using LiteLLM.
"""

import json
from typing import Optional, Dict, Any
from utils.logger import logger
from ..base_llm_client import BaseLLMClient
from ..llm_config import LLMConfig
from ..exceptions import LLMError

class CommandParsingService(BaseLLMClient):
    """Service for parsing user commands."""

    def __init__(self, config: Optional[LLMConfig] = None):
        super().__init__(config)

    async def parse_command(self, user_prompt: str, model: Optional[str] = None) -> Dict[str, Any]:
        """
        Parse the user's command from their input.
        """
        try:
            prompt = self._build_parsing_prompt(user_prompt)
            
            messages = [{"role": "system", "content": "You are a command parsing assistant."},
                        {"role": "user", "content": prompt}]

            response = await self._make_request(
                messages=messages,
                model=model or self.config.default_models.command_parsing,
                timeout=self.config.timeouts.command_parsing,
                format_json=True,
                operation_name="command parsing"
            )

            parsed_json = json.loads(response.choices[0].message.content)
            return parsed_json

        except (LLMError, json.JSONDecodeError) as e:
            logger.error(f"Command parsing failed: {e}")
            return {"command": "chat", "url": None, "error": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error in command parsing: {e}")
            return {"command": "chat", "url": None, "error": str(e)}

    def _build_parsing_prompt(self, user_prompt: str) -> str:
        return (
            f"Parse the following user prompt and extract the command and URL. "
            f"The command should be one of: 'crawl', 'chat', 'exit'. "
            f"If a URL is present, extract it. If no specific command is clear, default to 'chat'. "
            f"Respond with JSON only. Example: {{'command': 'crawl', 'url': 'https://example.com'}}\n\n"
            f"User prompt: '{user_prompt}'"
        )
