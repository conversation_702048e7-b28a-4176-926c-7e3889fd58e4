"""
Chat service for conversational interactions using LiteLLM.
"""

from typing import Optional, List, Dict
from utils.logger import logger
from ..base_llm_client import BaseLLMClient
from ..llm_config import LLMConfig
from ..exceptions import LLMError

class ChatService(BaseLLMClient):
    """Service for handling chat interactions."""

    def __init__(self, config: Optional[LLMConfig] = None):
        super().__init__(config)

    async def get_chat_response(
        self,
        prompt: str,
        model: Optional[str] = None,
        history: Optional[List[Dict[str, str]]] = None
    ) -> str:
        """
        Get a response from the chat model.
        """
        try:
            messages = history or []
            messages.append({"role": "user", "content": prompt})

            if self.config.log_prompts and self.config.debug_logging:
                logger.debug(f"Chat prompt: {prompt[:200]}...")

            response = await self._make_request(
                messages=messages,
                model=model or self.config.default_models.chat,
                timeout=self.config.timeouts.chat,
                operation_name="chat response"
            )

            chat_response = response.choices[0].message.content.strip()

            if not chat_response:
                logger.warning("LLM returned empty chat response")
                return "I'm sorry, I couldn't generate a response."

            return chat_response

        except LLMError as e:
            logger.error(f"Chat response generation failed: {e}")
            return f"Error communicating with the LLM: {e}"
        except Exception as e:
            logger.error(f"Unexpected error in chat response: {e}")
            return f"An unexpected error occurred: {str(e)}"
