from .llm_config import LLMConfig
from .services.summary_service import SummaryService
from .services.chat_service import ChatService
from .services.command_parsing_service import CommandParsingService
from .services.company_data_service import CompanyDataExtractionService


class LLMServiceFactory:
    _instance = None

    def __init__(self, config=None):
        if LLMServiceFactory._instance is not None:
            raise Exception("This class is a singleton!")
        self.config = config or LLMConfig.create_from_ini()
        self._summary_service = None
        self._chat_service = None
        self._command_parsing_service = None
        self._company_data_service = None
        LLMServiceFactory._instance = self

    def get_summary_service(self):
        if self._summary_service is None:
            self._summary_service = SummaryService(self.config)
        return self._summary_service

    def get_chat_service(self):
        if self._chat_service is None:
            self._chat_service = ChatService(self.config)
        return self._chat_service

    def get_command_parsing_service(self):
        if self._command_parsing_service is None:
            self._command_parsing_service = CommandParsingService(self.config)
        return self._command_parsing_service

    def get_company_data_service(self):
        if self._company_data_service is None:
            self._company_data_service = CompanyDataExtractionService(self.config)
        return self._company_data_service


def get_default_llm_factory():
    if LLMServiceFactory._instance is None:
        LLMServiceFactory()
    return LLMServiceFactory._instance
