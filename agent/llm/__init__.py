"""
LLM Client Package powered by LiteLLM.

This package provides a unified interface for interacting with various Large Language Models
(LLMs) like OpenAI, Groq, and local Ollama instances.
"""

from .client import (
    get_summary_from_llm,
    get_chat_response_from_llm,
    parse_command_from_prompt_llm,
    extract_company_data_from_impressum_llm,
)

# The factory is an internal implementation detail and not part of the public API.

__all__ = [
    'get_summary_from_llm',
    'get_chat_response_from_llm',
    'parse_command_from_prompt_llm',
    'extract_company_data_from_impressum_llm',
]
