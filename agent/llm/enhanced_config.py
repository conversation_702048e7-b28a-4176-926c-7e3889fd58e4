"""
Enhanced LLM configuration with profile support and environment variable substitution.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict
import configparser
import os
import re


@dataclass
class ProviderProfile:
    """Configuration profile for a specific provider setup."""
    name: str
    provider: str
    model: str
    description: str = ""


@dataclass
class EnhancedLLMConfig:
    """Enhanced LLM configuration with profile support."""
    active_provider: str = "ollama_local"
    profiles: Dict[str, ProviderProfile] = field(default_factory=dict)
    provider_settings: Dict[str, Dict[str, str]] = field(default_factory=dict)
    
    # Current active configuration (derived from active profile)
    current_provider: str = "ollama"
    current_model: str = "qwen3"
    
    @classmethod
    def create_from_ini(cls, config_file: str = 'config.ini') -> 'EnhancedLLMConfig':
        """Create configuration from INI file with profile and environment variable support."""
        config = configparser.ConfigParser()
        config.read(config_file)
        
        enhanced_config = cls()
        
        # Get active provider
        if config.has_section('litellm') and config.has_option('litellm', 'active_provider'):
            enhanced_config.active_provider = config.get('litellm', 'active_provider')
        
        # Load all profiles
        profiles = {}
        for section_name in config.sections():
            if section_name.startswith('profile_'):
                profile_name = section_name[8:]  # Remove 'profile_' prefix
                
                if (config.has_option(section_name, 'provider') and 
                    config.has_option(section_name, 'model')):
                    
                    profile = ProviderProfile(
                        name=profile_name,
                        provider=config.get(section_name, 'provider'),
                        model=config.get(section_name, 'model'),
                        description=config.get(section_name, 'description', fallback="")
                    )
                    profiles[profile_name] = profile
        
        enhanced_config.profiles = profiles
        
        # Load provider-specific settings with environment variable substitution
        provider_settings = {}
        for provider in ['ollama', 'openai', 'groq']:
            if config.has_section(provider):
                settings = {}
                for key, value in config.items(provider):
                    # Substitute environment variables
                    settings[key] = enhanced_config._substitute_env_vars(value)
                provider_settings[provider] = settings
        
        enhanced_config.provider_settings = provider_settings
        
        # Set current active configuration
        enhanced_config._set_active_configuration()
        
        return enhanced_config
    
    def _substitute_env_vars(self, value: str) -> str:
        """Substitute environment variables in configuration values."""
        # Pattern: ${VAR_NAME} or ${VAR_NAME:default_value}
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        
        def replace_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ""
            return os.getenv(var_name, default_value)
        
        return re.sub(pattern, replace_var, value)
    
    def _set_active_configuration(self):
        """Set current provider and model based on active profile."""
        if self.active_provider in self.profiles:
            profile = self.profiles[self.active_provider]
            self.current_provider = profile.provider
            self.current_model = profile.model
        else:
            # Fallback to default
            self.current_provider = "ollama"
            self.current_model = "qwen3"
    
    def switch_provider(self, profile_name: str) -> bool:
        """Switch to a different provider profile."""
        if profile_name in self.profiles:
            self.active_provider = profile_name
            self._set_active_configuration()
            return True
        return False
    
    def get_available_profiles(self) -> List[ProviderProfile]:
        """Get list of all available profiles."""
        return list(self.profiles.values())
    
    def get_provider_settings(self, provider: str) -> Dict[str, str]:
        """Get settings for a specific provider."""
        return self.provider_settings.get(provider, {})
    
    def get_current_api_key(self) -> Optional[str]:
        """Get API key for current provider."""
        settings = self.get_provider_settings(self.current_provider)
        return settings.get('api_key')
    
    def get_current_base_url(self) -> Optional[str]:
        """Get base URL for current provider."""
        settings = self.get_provider_settings(self.current_provider)
        return settings.get('base_url')
    
    def to_legacy_config(self):
        """Convert to legacy LLMConfig format for backward compatibility."""
        from .llm_config import LLMConfig
        
        return LLMConfig(
            provider=self.current_provider,
            primary_model=self.current_model,
            api_key=self.get_current_api_key(),
            base_url=self.get_current_base_url()
        )


def create_provider_switcher():
    """Create a simple provider switcher utility."""
    
    def switch_provider(profile_name: str, config_file: str = 'config.ini'):
        """Switch provider by updating config file."""
        config = configparser.ConfigParser()
        config.read(config_file)
        
        if not config.has_section('litellm'):
            config.add_section('litellm')
        
        config.set('litellm', 'active_provider', profile_name)
        
        with open(config_file, 'w') as f:
            config.write(f)
        
        print(f"✅ Switched to provider profile: {profile_name}")
    
    return switch_provider


def list_available_profiles(config_file: str = 'config.ini'):
    """List all available provider profiles."""
    enhanced_config = EnhancedLLMConfig.create_from_ini(config_file)
    
    print("📋 Available Provider Profiles:")
    print("=" * 50)
    
    for profile in enhanced_config.get_available_profiles():
        active_marker = "🔥" if profile.name == enhanced_config.active_provider else "  "
        print(f"{active_marker} {profile.name}")
        print(f"   Provider: {profile.provider}")
        print(f"   Model: {profile.model}")
        if profile.description:
            print(f"   Description: {profile.description}")
        print()
    
    print(f"Current active: {enhanced_config.active_provider}")
    return enhanced_config.get_available_profiles()


if __name__ == "__main__":
    # Demo usage
    print("🧪 Enhanced LLM Configuration Demo")
    list_available_profiles()
