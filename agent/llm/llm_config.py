from dataclasses import dataclass, field
from typing import List, Optional
import configparser
import os

@dataclass
class RetryConfig:
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 10.0
    exponential_base: float = 2.0

@dataclass
class TimeoutsConfig:
    default: int = 30
    summary: int = 120
    chat: int = 60
    command_parsing: int = 20
    company_data: int = 90

@dataclass
class DefaultModelsConfig:
    summary: str = "ollama/qwen3"
    chat: str = "ollama/qwen3"
    command_parsing: str = "ollama/qwen3"
    company_data: str = "ollama/qwen3"

@dataclass
class LLMConfig:
    provider: str = "ollama"
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    primary_model: str = "ollama/qwen3"
    fallback_models: List[str] = field(default_factory=list)
    max_content_length: int = 10000
    truncated_content_length: int = 3000
    log_prompts: bool = True
    log_responses: bool = False
    debug_logging: bool = False
    retry: RetryConfig = field(default_factory=RetryConfig)
    timeouts: TimeoutsConfig = field(default_factory=TimeoutsConfig)
    default_models: DefaultModelsConfig = field(default_factory=DefaultModelsConfig)

    @classmethod
    def create_from_ini(cls, config_file: str = 'config.ini') -> 'LLMConfig':
        config = configparser.ConfigParser()
        config.read(config_file)
        
        llm_config = cls()
        
        if 'litellm' in config:
            litellm_section = config['litellm']
            llm_config.provider = litellm_section.get('provider', llm_config.provider)
            llm_config.primary_model = litellm_section.get('primary_model', llm_config.primary_model)
            fallback_str = litellm_section.get('fallback_models', '')
            if fallback_str:
                llm_config.fallback_models = [m.strip() for m in fallback_str.split(',')]

        if llm_config.provider == 'ollama' and 'ollama' in config:
            ollama_section = config['ollama']
            llm_config.base_url = ollama_section.get('base_url')
        elif llm_config.provider == 'openai' and 'openai' in config:
            openai_section = config['openai']
            llm_config.api_key = openai_section.get('api_key') or os.getenv('OPENAI_API_KEY')
        elif llm_config.provider == 'groq' and 'groq' in config:
            groq_section = config['groq']
            llm_config.api_key = groq_section.get('api_key') or os.getenv('GROQ_API_KEY')
            
        return llm_config
