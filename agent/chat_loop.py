# Import necessary functions from other modules.
# `summarize_website` and `extract_impressum` are the core agent commands.
# `normalize_url` ensures URLs have a consistent format (e.g., adding 'https://').
# `is_valid_url` checks if a string is a well-formed URL before processing.
from agent.commands import extract_impressum, process_local_file, summarize_website, extract_sitemap
from agent.llm import (
    get_chat_response_from_llm as get_chat_response_from_ollama,  # Alias for compatibility
    parse_command_from_prompt_llm as parse_command_from_prompt,  # Alias for compatibility
)
from utils.logger import logger
from utils.url_utils import normalize_url
from utils.validation_utils import is_valid_url

# --- Command Registry ---
# This dictionary maps command strings to their handlers and properties.
# It makes the system modular and easy to extend with new commands.
COMMAND_REGISTRY = {
    "summarize": {
        "handler": summarize_website,
        "requires_url": True,
        "is_file_path": False,
        "user_facing_name": "summarize",
        "supports_media": True,  # Flag to indicate support for screenshot/PDF
    },
    "impressum": {
        "handler": extract_impressum,
        "requires_url": True,
        "is_file_path": False,
        "user_facing_name": "find company data for",
        "supports_media": False,  # Impressum doesn't need screenshots/PDFs
    },
    "sitemap": {
        "handler": extract_sitemap,
        "requires_url": True,
        "is_file_path": False,
        "user_facing_name": "extract sitemap from",
        "supports_media": False,  # Sitemap doesn't need screenshots/PDFs
    },
    "local": {
        "handler": process_local_file,
        "requires_url": True,
        "is_file_path": True,
        "user_facing_name": "process local file",
        "supports_media": False,  # Local files don't need screenshots/PDFs
    },
}


def _display_result(command_name: str, result: any):
    """Formats and prints the result of a command."""
    if not result:
        logger.info(f"Command '{command_name}' completed with no output.")
        return

    if command_name == "summarize":
        if isinstance(result, dict) and "error" in result:
            print(f"\n❌ Error summarizing website: {result['error']}")
            return

        if isinstance(result, dict) and "summary" in result:
            print("\nSummary:")
            print(result["summary"])

            # Display file paths if available
            if "files" in result:
                files = result["files"]
                print("\n📁 Generated files:")
                if files.get("html"):
                    print(f"  - HTML: {files['html']}")
                if files.get("summary"):
                    print(f"  - Summary: {files['summary']}")
                if files.get("screenshot"):
                    print(f"  - Screenshot: {files['screenshot']}")
                if files.get("pdf"):
                    print(f"  - PDF: {files['pdf']}")
        else:
            # Fallback for backward compatibility
            print("\nSummary:")
            print(result)

    elif command_name == "impressum":
        if isinstance(result, dict) and "error" in result:
            print(f"\n❌ Error extracting data: {result['error']}")
            if "troubleshooting" in result:
                print(f"   -> {result['troubleshooting']}")
        elif isinstance(result, dict):
            print("\n✅ Extracted company data:")
            for key, value in result.items():
                print(f"  - {key.replace('_', ' ').title()}: {value}")
    elif command_name == "local":
        print("\n📄 Extracted content from local file:")
        print(result)
    else:
        # Generic fallback for unknown result types or commands
        print(f"\n✅ Command Result for '{command_name}':\n{result}")


async def chat_loop():
    """
    Runs the main interactive loop for the Crawl4AI agent.
    It waits for user input, parses commands, and executes the corresponding actions.
    If the input is not a recognized command, it's treated as a chat prompt for Ollama.
    """
    # Display a welcome message and instructions to the user upon startup.
    print("🔍 Crawl4AI Agent: Ready to receive instructions.")
    print(
        "You can use commands like 'summarize <url>', 'impressum <url>', or 'sitemap <url>'."
    )
    print("Add '--screenshot' or '--pdf' to capture media (e.g., 'summarize example.com --screenshot').")
    print("🧠 Schema Learning: Use 'smart crawl <url>' for AI-powered structured data extraction.")
    print("🗺️ Sitemap Extraction: Use 'sitemap <url>' to extract all URLs from website sitemaps.")
    print("Any other input will be treated as a chat prompt for the AI.")
    print("Type 'exit' to quit.\n")
    # Start an infinite loop to continuously listen for user commands.
    while True:
        # Prompt the user for an instruction and remove leading/trailing whitespace.
        prompt = input("🧠 Your instruction: ").strip()

        # Check for the 'exit' command to terminate the agent.
        if prompt.lower() == "exit":
            print("👋 Goodbye!")
            break

        # Check for media flags
        screenshot = "--screenshot" in prompt.lower()
        pdf = "--pdf" in prompt.lower()

        # Remove the flags from the prompt for parsing
        clean_prompt = prompt.lower().replace(
            "--screenshot", "").replace("--pdf", "").strip()

        # --- NLU-based Command Routing ---
        logger.info("Parsing user command...")
        # Use Ollama to understand the user's intent.
        parsed_command = await parse_command_from_prompt(clean_prompt)

        command_name = parsed_command.get("command", "chat")
        url_or_path = parsed_command.get("url")

        # Look up the command in the registry.
        command_details = COMMAND_REGISTRY.get(command_name)

        if command_details:
            # A recognized command was found.
            result = None
            handler = command_details["handler"]

            if command_details["requires_url"]:
                if not url_or_path:
                    # The command needs a URL, but none was provided.
                    user_facing_name = command_details["user_facing_name"]
                    logger.error(
                        f"❌ You asked me to {user_facing_name}, but I couldn't find a URL or path in your request."
                    )
                    logger.error(
                        f"   Please try again with a URL/path, like: {command_name} example.com")
                    continue

                if command_details.get("is_file_path", False):
                    result = await handler(url_or_path)
                else:
                    # It's a URL, so normalize and validate it.
                    normalized_url = normalize_url(url_or_path)
                    if is_valid_url(normalized_url):
                        # Check if command supports media options
                        if command_details.get("supports_media", False):
                            result = await handler(normalized_url, screenshot=screenshot, pdf=pdf)
                            if screenshot or pdf:
                                media_types = []
                                if screenshot:
                                    media_types.append("screenshot")
                                if pdf:
                                    media_types.append("PDF")
                                logger.info(
                                    f"Capturing {' and '.join(media_types)} for {normalized_url}")
                        else:
                            # Command doesn't support media options
                            if screenshot or pdf:
                                logger.warning(
                                    f"The '{command_name}' command doesn't support screenshots or PDFs. Ignoring these flags.")
                            result = await handler(normalized_url)
                    else:
                        logger.error(
                            f"❌ The URL '{url_or_path}' provided in your request looks invalid. Please try again."
                        )
                        continue
            else:
                # For future commands that don't require a URL.
                result = await handler()

            _display_result(command_name, result)
        else:
            # Fallback for 'chat' or unknown commands.
            if command_name and command_name != "chat":
                logger.warning(
                    f"Unknown command '{command_name}' from NLU. Treating as a chat prompt.")
            logger.info("Sending prompt to Ollama for a chat response...")
            response = await get_chat_response_from_ollama(clean_prompt)
            print(f"\n💬 Ollama:\n{response}\n")
