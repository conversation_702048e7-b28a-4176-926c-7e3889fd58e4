"""
Browser-based web search utilities for Bing and Google (no official API required).
Uses Playwright to automate browser search and scrape results.
"""
import asyncio
from typing import List, Dict
from playwright.async_api import async_playwright

async def browser_search_bing(query: str, max_results: int = 10, lang: str = "de") -> List[Dict]:
    results = []
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        # Bing search URL with language
        url = f"https://www.bing.com/search?q={query}&setlang={lang}"
        await page.goto(url)
        # Wait for results
        await page.wait_for_selector("li.b_algo")
        items = await page.query_selector_all("li.b_algo")
        for item in items[:max_results]:
            title = await item.query_selector_eval("h2", "el => el.textContent")
            link = await item.query_selector_eval("h2 a", "el => el.href")
            snippet = await item.query_selector_eval(".b_caption p", "el => el.textContent") if await item.query_selector(".b_caption p") else ""
            results.append({
                "title": title.strip() if title else "",
                "url": link.strip() if link else "",
                "snippet": snippet.strip() if snippet else ""
            })
        await browser.close()
    return results

async def browser_search_google(query: str, max_results: int = 10, lang: str = "de") -> List[Dict]:
    results = []
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        # Google search URL with language
        url = f"https://www.google.com/search?q={query}&hl={lang}"
        await page.goto(url)
        # Wait for results
        await page.wait_for_selector("div.g")
        items = await page.query_selector_all("div.g")
        for item in items[:max_results]:
            title = await item.query_selector_eval("h3", "el => el.textContent") if await item.query_selector("h3") else ""
            link = await item.query_selector_eval("a", "el => el.href") if await item.query_selector("a") else ""
            snippet = await item.query_selector_eval("span.aCOpRe", "el => el.textContent") if await item.query_selector("span.aCOpRe") else ""
            results.append({
                "title": title.strip() if title else "",
                "url": link.strip() if link else "",
                "snippet": snippet.strip() if snippet else ""
            })
        await browser.close()
    return results

# Example usage:
# asyncio.run(browser_search_bing("Bürodrehstuhl online kaufen", lang="de"))
# asyncio.run(browser_search_google("Bürodrehstuhl online kaufen", lang="de"))
