"""
LangGraph Tools for the Crawl4AI Agent

This module contains @tool decorated functions that can be used by the LangGraph
agent to perform various crawling and data extraction tasks.
"""

import os
from typing import Dict, Any, Optional, Tuple, List, Union, Type
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from ddgs import DDGS

# Import existing functionality
from crawler import (
    crawl_website, crawl_local_file, save_links_to_file, _extract_page_title,
    crawl_multiple_websites, smart_crawler, SchemaLearner,
    download_html, download_multiple_html
)
from crawler.company_data import crawl_impressum, crawl_multiple_impressums
from utils.find_link_utils import find_impressum_link
from utils.file_utils import save_json_to_dir, save_summary_to_dir
from utils.preprocessing_utils import preprocess_impressum_content, extract_text_from_html
from agent.llm import get_summary_from_llm as get_summary, extract_company_data_from_impressum_llm as extract_company_data
from utils.logger import logger

# Import statistics tools
from crawler.statistics.tools import (
    get_crawl_statistics_summary,
    analyze_domain_performance,
    analyze_page_performance,
    analyze_time_based_trends,
    get_recent_crawl_activity,
    cleanup_old_statistics
)


# Input schemas for tools
class CrawlWebsiteInput(BaseModel):
    """Input schema for crawl_website_tool"""
    url: str = Field(description="The URL to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture a screenshot")
    pdf: bool = Field(default=False, description="Whether to generate a PDF")


class CrawlWithLinksInput(BaseModel):
    """Input schema for crawl_with_links_tool"""
    url: str = Field(description="URL to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture a screenshot")
    pdf: bool = Field(default=False, description="Whether to generate a PDF")
    extract_links: bool = Field(
        default=True, description="Whether to extract and categorize internal/external links")
    save_links: bool = Field(
        default=True, description="Whether to save extracted links to a JSON file")


class CrawlMultipleWebsitesInput(BaseModel):
    """Input schema for crawl_multiple_websites_tool"""
    urls: Union[str, List[str]] = Field(
        description="Single URL or list of URLs to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture screenshots")
    pdf: bool = Field(default=False, description="Whether to generate PDFs")
    extract_links: bool = Field(
        default=False, description="Whether to extract and categorize links")


class CrawlMultipleImpressumsInput(BaseModel):
    """Input schema for crawl_multiple_impressums_tool"""
    urls: Union[str, List[str]] = Field(
        description="Single URL or list of URLs to crawl impressums from")


class FindImpressumLinkInput(BaseModel):
    """Input schema for find_impressum_link_tool"""
    html_content: str = Field(
        description="HTML content to search for impressum links")
    base_url: str = Field(description="Base URL for resolving relative links")


class ExtractCompanyDataInput(BaseModel):
    """Input schema for extract_company_data_tool"""
    content: str = Field(description="Content to extract company data from")
    url: str = Field(description="URL of the source for context")


class GenerateSummaryInput(BaseModel):
    """Input schema for generate_summary_tool"""
    content: str = Field(description="Content to summarize")


class ProcessLocalFileInput(BaseModel):
    """Input schema for process_local_file_tool"""
    file_path: str = Field(description="Path to the local file to process")


class WebSearchInput(BaseModel):
    """Input schema for web_search_tool"""
    query: str = Field(description="Search query to search for")
    max_results: int = Field(
        default=10, description="Maximum number of search results to return")
    region: str = Field(
        default="us-en", description="Region for search results (e.g., 'us-en', 'de-de')")
    safesearch: str = Field(
        default="moderate", description="Safe search setting: 'on', 'moderate', 'off'")
    timelimit: Optional[str] = Field(
        default=None, description="Time limit for results: 'd' (day), 'w' (week), 'm' (month), 'y' (year)")


class SearchAndCrawlInput(BaseModel):
    """Input schema for search_and_crawl_tool"""
    query: str = Field(description="Search query to search for")
    max_results: int = Field(
        default=5, description="Maximum number of search results to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture screenshots of crawled pages")
    pdf: bool = Field(
        default=False, description="Whether to generate PDFs of crawled pages")


class SmartCrawlerInput(BaseModel):
    """Input schema for smart_crawler_tool"""
    url: str = Field(description="The URL to crawl")
    target_model_schema: Dict[str, Any] = Field(
        description="JSON schema of the target Pydantic model")
    provider: str = Field(default="ollama/qwen3",
                          description="LLM provider for schema learning")
    api_token: Optional[str] = Field(
        default=None, description="API token for the LLM provider")
    force_learn: bool = Field(
        default=False, description="Force learning new schema even if cached one exists")
    schema_dir: str = Field(default="./learned_schemas",
                            description="Directory to store learned schemas")


class DownloadHtmlInput(BaseModel):
    """Input schema for download_html_tool"""
    url: str = Field(description="The URL to download HTML from")
    html_dir: str = Field(default="scraped_html",
                          description="Directory to save HTML files in")
    screenshot: bool = Field(
        default=False, description="Whether to capture a screenshot")
    pdf: bool = Field(default=False, description="Whether to generate a PDF")
    wait_until: str = Field(
        default="networkidle", description="When to consider page loading complete")
    page_timeout: int = Field(
        default=60000, description="Page load timeout in milliseconds")


class DownloadMultipleHtmlInput(BaseModel):
    """Input schema for download_multiple_html_tool"""
    urls: Union[str, List[str]] = Field(
        description="Single URL or list of URLs to download HTML from")
    html_dir: str = Field(default="scraped_html",
                          description="Directory to save HTML files in")
    screenshot: bool = Field(
        default=False, description="Whether to capture screenshots")
    pdf: bool = Field(default=False, description="Whether to generate PDFs")
    wait_until: str = Field(
        default="networkidle", description="When to consider page loading complete")
    page_timeout: int = Field(
        default=60000, description="Page load timeout in milliseconds")


# LangGraph Tools
@tool("crawl_website_tool", args_schema=CrawlWebsiteInput)
async def crawl_website_tool(url: str, screenshot: bool = False, pdf: bool = False) -> Dict[str, Any]:
    """
    Crawl a website and return its content along with file paths.

    Args:
        url: The URL to crawl
        screenshot: Whether to capture a screenshot
        pdf: Whether to generate a PDF

    Returns:
        Dictionary containing markdown content and file paths
    """
    try:
        logger.info(f"Crawling website: {url}")
        result = await crawl_website(url, screenshot, pdf, extract_links=False)
        markdown, html_path, screenshot_path, pdf_path, links_data, page_title = result

        if str(markdown).startswith("Error:"):
            return {"error": str(markdown)}

        return {
            "markdown_content": str(markdown),
            "saved_html_path": html_path,
            "screenshot_path": screenshot_path,
            "pdf_path": pdf_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error crawling website {url}: {e}")
        return {"error": f"Failed to crawl website: {str(e)}"}


@tool("crawl_with_links_tool", args_schema=CrawlWithLinksInput)
async def crawl_with_links_tool(
    url: str,
    screenshot: bool = False,
    pdf: bool = False,
    extract_links: bool = True,
    save_links: bool = True
) -> Dict[str, Any]:
    """
    Crawl a website and extract internal/external links along with content.

    Args:
        url (str): The URL to crawl
        screenshot (bool): Whether to capture a screenshot
        pdf (bool): Whether to generate a PDF
        extract_links (bool): Whether to extract and categorize links
        save_links (bool): Whether to save links to a JSON file

    Returns:
        Dict containing content, file paths, and link information
    """
    try:
        # Use the enhanced crawl_website function with link extraction
        result = await crawl_website(url, screenshot, pdf, extract_links)
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

        response = {
            "url": url,
            "page_title": page_title,
            "markdown_content": markdown_content,
            "html_path": html_path,
            "screenshot_path": screenshot_path,
            "pdf_path": pdf_path,
            "success": True
        }

        # Add link information if extracted
        if links_data:
            response["links"] = links_data
            response["link_summary"] = {
                "total_internal": len(links_data['internal']),
                "total_external": len(links_data['external']),
                "total_links": len(links_data['internal']) + len(links_data['external'])
            }

            # Save links to file if requested
            if save_links:
                links_file_path = save_links_to_file(
                    links_data, url, page_title)
                response["links_file_path"] = links_file_path

            logger.info(
                f"Extracted {response['link_summary']['total_links']} links from {url}")

        return response

    except Exception as e:
        logger.error(f"Error crawling website with links {url}: {e}")
        return {"error": f"Failed to crawl website with links: {str(e)}"}


@tool("crawl_multiple_websites_tool", args_schema=CrawlMultipleWebsitesInput)
async def crawl_multiple_websites_tool(
    urls: Union[str, List[str]],
    screenshot: bool = False,
    pdf: bool = False,
    extract_links: bool = False
) -> Dict[str, Any]:
    """
    Crawl multiple websites and return their content.

    Args:
        urls: Single URL string or list of URLs to crawl
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs
        extract_links: Whether to extract and categorize links

    Returns:
        Dict containing results for all crawled websites
    """
    try:
        logger.info(f"Crawling multiple websites: {urls}")
        results = await crawl_multiple_websites(urls, screenshot, pdf, extract_links)

        # Separate successful and failed results
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        response = {
            "total_urls": len(results),
            "successful_crawls": len(successful_results),
            "failed_crawls": len(failed_results),
            "results": results,
            "success": len(successful_results) > 0
        }

        # Add summary information
        if successful_results:
            response["successful_urls"] = [r["url"]
                                           for r in successful_results]

        if failed_results:
            response["failed_urls"] = [r["url"] for r in failed_results]
            response["errors"] = {r["url"]: r["error"] for r in failed_results}

        logger.info(
            f"Completed crawling {len(results)} URLs. Success: {len(successful_results)}/{len(results)}")
        return response

    except Exception as e:
        logger.error(f"Error crawling multiple websites: {e}")
        return {"error": f"Failed to crawl multiple websites: {str(e)}"}


@tool("crawl_multiple_impressums_tool", args_schema=CrawlMultipleImpressumsInput)
async def crawl_multiple_impressums_tool(urls: Union[str, List[str]]) -> Dict[str, Any]:
    """
    Crawl impressum pages from multiple websites.

    Args:
        urls: Single URL string or list of URLs to crawl impressums from

    Returns:
        Dict containing impressum results for all websites
    """
    try:
        logger.info(f"Crawling multiple impressums: {urls}")
        results = await crawl_multiple_impressums(urls)

        # Separate successful and failed results
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        response = {
            "total_urls": len(results),
            "successful_crawls": len(successful_results),
            "failed_crawls": len(failed_results),
            "results": results,
            "success": len(successful_results) > 0
        }

        # Add summary information
        if successful_results:
            response["successful_urls"] = [r["url"]
                                           for r in successful_results]

        if failed_results:
            response["failed_urls"] = [r["url"] for r in failed_results]
            response["errors"] = {r["url"]: r["error"] for r in failed_results}

        logger.info(
            f"Completed crawling impressums from {len(results)} URLs. Success: {len(successful_results)}/{len(results)}")
        return response

    except Exception as e:
        logger.error(f"Error crawling multiple impressums: {e}")
        return {"error": f"Failed to crawl multiple impressums: {str(e)}"}


@tool("crawl_impressum_tool")
async def crawl_impressum_tool(base_url: str) -> Dict[str, Any]:
    """
    Find and crawl the impressum/legal notice page of a website.

    Args:
        base_url: The base URL of the website

    Returns:
        Dictionary containing impressum content and file path
    """
    try:
        logger.info(f"Crawling impressum for: {base_url}")
        markdown, html_path = await crawl_impressum(base_url)

        if not markdown:
            return {"error": f"Could not find impressum page for {base_url}"}

        return {
            "markdown_content": markdown,
            "saved_html_path": html_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error crawling impressum for {base_url}: {e}")
        return {"error": f"Failed to crawl impressum: {str(e)}"}


@tool("find_impressum_link_tool", args_schema=FindImpressumLinkInput)
async def find_impressum_link_tool(html_content: str, base_url: str) -> Dict[str, Any]:
    """
    Find impressum/legal notice links in HTML content.

    Args:
        html_content: HTML content to search
        base_url: Base URL for resolving relative links

    Returns:
        Dictionary containing the impressum URL if found
    """
    try:
        impressum_url = await find_impressum_link(html_content, base_url)
        if impressum_url:
            return {"impressum_url": impressum_url, "success": True}
        else:
            return {"error": "Impressum link not found", "success": False}
    except Exception as e:
        logger.error(f"Error finding impressum link: {e}")
        return {"error": f"Failed to find impressum link: {str(e)}"}


@tool("extract_company_data_tool", args_schema=ExtractCompanyDataInput)
async def extract_company_data_tool(content: str, url: str) -> Dict[str, Any]:
    """
    Extract company data from content using an LLM.

    Args:
        content: Content to extract data from
        url: Source URL for context

    Returns:
        Dictionary containing extracted company data
    """
    try:
        logger.info(f"Extracting company data from content for: {url}")
        company_data = await extract_company_data(content, url)

        if "error" in company_data:
            return company_data

        # Save extracted data to JSON
        json_path = save_json_to_dir(company_data, url)

        return {
            "company_data": company_data,
            "saved_json_path": json_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error extracting company data: {e}")
        return {"error": f"Failed to extract company data: {str(e)}"}


@tool("generate_summary_tool", args_schema=GenerateSummaryInput)
async def generate_summary_tool(content: str) -> Dict[str, Any]:
    """
    Generate a summary of the given content using an LLM.

    Args:
        content: Content to summarize

    Returns:
        Dictionary containing the generated summary
    """
    try:
        logger.info("Generating summary using LLM")
        summary = await get_summary(content)

        if summary.startswith("Error"):
            return {"error": summary}

        return {
            "summary": summary,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        return {"error": f"Failed to generate summary: {str(e)}"}


@tool("save_json_data_tool")
def save_json_data_tool(data: Dict[str, Any], url: str) -> Dict[str, Any]:
    """
    Save structured data as JSON file.

    Args:
        data: Data to save
        url: Source URL for naming

    Returns:
        Dictionary containing the saved file path
    """
    try:
        logger.info(f"Saving JSON data for: {url}")
        json_path = save_json_to_dir(data, url)

        return {
            "json_path": json_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error saving JSON data: {e}")
        return {"error": f"Failed to save JSON data: {str(e)}"}


@tool("save_summary_tool")
def save_summary_tool(summary: str, url: str) -> Dict[str, Any]:
    """
    Save summary as markdown file.

    Args:
        summary: Summary content to save
        url: Source URL for naming

    Returns:
        Dictionary containing the saved file path
    """
    try:
        logger.info(f"Saving summary for: {url}")
        summary_path = save_summary_to_dir(summary, url)

        return {
            "summary_path": summary_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error saving summary: {e}")
        return {"error": f"Failed to save summary: {str(e)}"}


@tool("web_search_tool", args_schema=WebSearchInput)
def web_search_tool(
    query: str,
    max_results: int = 10,
    region: str = "us-en",
    safesearch: str = "moderate",
    timelimit: Optional[str] = None
) -> Dict[str, Any]:
    """
    Search the web using DuckDuckGo and return search results.

    Args:
        query: The search query
        max_results: Maximum number of results
        region: Region for search (e.g., 'us-en')
        safesearch: Safe search level ('on', 'moderate', 'off')
        timelimit: Time limit for results ('d', 'w', 'm', 'y')

    Returns:
        Dictionary containing search results
    """
    try:
        logger.info(f"Searching web for: {query}")
        with DDGS() as ddgs:
            results = list(ddgs.text(query, max_results=max_results, region=region, safesearch=safesearch, timelimit=timelimit))

        return {
            "search_results": results,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error performing web search: {e}")
        return {"error": f"Failed to perform web search: {str(e)}"}


@tool("aweb_search_tool", args_schema=WebSearchInput)
async def aweb_search_tool(
    query: str,
    max_results: int = 10,
    region: str = "us-en",
    safesearch: str = "moderate",
    timelimit: Optional[str] = None
) -> Dict[str, Any]:
    """
    Asynchronously search the web using DuckDuckGo and return search results.

    Args:
        query: The search query
        max_results: Maximum number of results
        region: Region for search (e.g., 'us-en')
        safesearch: Safe search level ('on', 'moderate', 'off')
        timelimit: Time limit for results ('d', 'w', 'm', 'y')

    Returns:
        Dictionary containing search results
    """
    try:
        logger.info(f"Async searching web for: {query}")
        async with DDGS() as ddgs:
            results = [
                r async for r in ddgs.text(
                    query,
                    max_results=max_results,
                    region=region,
                    safesearch=safesearch,
                    timelimit=timelimit
                )
            ]

        return {
            "search_results": results,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error performing async web search: {e}")
        return {"error": f"Failed to perform async web search: {str(e)}"}


@tool("search_and_crawl_tool", args_schema=SearchAndCrawlInput)
async def search_and_crawl_tool(
    query: str,
    max_results: int = 5,
    screenshot: bool = False,
    pdf: bool = False
) -> Dict[str, Any]:
    """
    Search the web and crawl the top results.

    Args:
        query: Search query
        max_results: Number of top results to crawl
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs

    Returns:
        Dictionary containing crawled results
    """
    try:
        logger.info(f"Searching and crawling for: {query}")
        search_results = await aweb_search_tool(
            query, max_results=max_results)

        if "error" in search_results:
            return search_results

        if "search_results" not in search_results:
            logger.error(f"'search_results' key not found in web search response: {search_results}")
            return {"error": "Invalid response from web_search_tool", "details": search_results}

        urls_to_crawl = [res['href']
                         for res in search_results["search_results"]]
        crawled_results = await crawl_multiple_websites_tool(urls_to_crawl, screenshot, pdf)

        return {
            "crawled_search_results": crawled_results,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error in search and crawl: {e}")
        return {"error": f"Failed to search and crawl: {str(e)}"}


@tool("smart_crawler_tool", args_schema=SmartCrawlerInput)
async def smart_crawler_tool(
    url: str,
    target_model_schema: Dict[str, Any],
    provider: str = "ollama/qwen3",
    api_token: Optional[str] = None,
    force_learn: bool = False,
    schema_dir: str = "./learned_schemas"
) -> Dict[str, Any]:
    """
    Crawl a website and extract structured data based on a learned schema.

    Args:
        url: The URL to crawl
        target_model_schema: JSON schema of the target Pydantic model
        provider: LLM provider for schema learning
        api_token: API token for the LLM provider
        force_learn: Force learning new schema
        schema_dir: Directory to store learned schemas

    Returns:
        Dictionary containing extracted data
    """
    try:
        logger.info(f"Starting smart crawl for: {url}")
        data = await smart_crawler(
            url,
            target_model_schema,
            provider,
            api_token,
            force_learn,
            schema_dir
        )
        return {"extracted_data": data, "success": True}
    except Exception as e:
        logger.error(f"Error during smart crawl: {e}")
        return {"error": f"Failed during smart crawl: {str(e)}"}


@tool("process_local_file_tool", args_schema=ProcessLocalFileInput)
async def process_local_file_tool(file_path: str) -> Dict[str, Any]:
    """
    Process a local file (HTML or other) and return its content.

    Args:
        file_path: Path to the local file

    Returns:
        Dictionary containing the file content
    """
    try:
        logger.info(f"Processing local file: {file_path}")
        content = await crawl_local_file(file_path)
        return {"content": content, "success": True}
    except Exception as e:
        logger.error(f"Error processing local file {file_path}: {e}")
        return {"error": f"Failed to process local file: {str(e)}"}


@tool("download_html_tool", args_schema=DownloadHtmlInput)
async def download_html_tool(
    url: str,
    html_dir: str = "scraped_html",
    screenshot: bool = False,
    pdf: bool = False,
    wait_until: str = "networkidle",
    page_timeout: int = 60000
) -> Dict[str, Any]:
    """
    Download HTML content from a URL and save it to a file.

    Args:
        url: The URL to download from
        html_dir: Directory to save the HTML file
        screenshot: Whether to capture a screenshot
        pdf: Whether to generate a PDF
        wait_until: When to consider page loading complete
        page_timeout: Page load timeout in ms

    Returns:
        Dictionary with the path to the saved HTML file
    """
    try:
        logger.info(f"Downloading HTML from: {url}")
        result = await download_html(
            url, html_dir, screenshot, pdf, wait_until, page_timeout)

        return {
            "html_path": result["html_path"],
            "screenshot_path": result["screenshot_path"],
            "pdf_path": result["pdf_path"],
            "success": True
        }
    except Exception as e:
        logger.error(f"Error downloading HTML from {url}: {e}")
        return {"error": f"Failed to download HTML: {str(e)}"}


@tool("download_multiple_html_tool", args_schema=DownloadMultipleHtmlInput)
async def download_multiple_html_tool(
    urls: Union[str, List[str]],
    html_dir: str = "scraped_html",
    screenshot: bool = False,
    pdf: bool = False,
    wait_until: str = "networkidle",
    page_timeout: int = 60000
) -> Dict[str, Any]:
    """
    Download HTML content from multiple URLs.

    Args:
        urls: List of URLs to download from
        html_dir: Directory to save HTML files
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs
        wait_until: When to consider page loading complete
        page_timeout: Page load timeout in ms

    Returns:
        Dictionary with results for each URL
    """
    try:
        logger.info(f"Downloading HTML from multiple URLs: {urls}")
        results = await download_multiple_html(
            urls, html_dir, screenshot, pdf, wait_until, page_timeout)

        return {
            "results": results,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error downloading multiple HTML files: {e}")
        return {"error": f"Failed to download multiple HTML files: {str(e)}"}


# Statistics Tools
@tool("get_crawl_statistics_summary_tool")
def get_crawl_statistics_summary_tool() -> Dict[str, Any]:
    """
    Get a summary of crawl statistics.

    Returns:
        Dictionary containing the statistics summary.
    """
    try:
        summary = get_crawl_statistics_summary()
        return {"summary": summary, "success": True}
    except Exception as e:
        return {"error": f"Failed to get statistics summary: {str(e)}"}


@tool("analyze_domain_performance_tool")
def analyze_domain_performance_tool(domain: str) -> Dict[str, Any]:
    """
    Analyze the crawl performance for a specific domain.

    Args:
        domain: The domain to analyze.

    Returns:
        Dictionary containing the performance analysis.
    """
    try:
        analysis = analyze_domain_performance(domain)
        return {"analysis": analysis, "success": True}
    except Exception as e:
        return {"error": f"Failed to analyze domain performance: {str(e)}"}


@tool("analyze_page_performance_tool")
def analyze_page_performance_tool(url: str) -> Dict[str, Any]:
    """
    Analyze the crawl performance for a specific page URL.

    Args:
        url: The URL to analyze.

    Returns:
        Dictionary containing the performance analysis.
    """
    try:
        analysis = analyze_page_performance(url)
        return {"analysis": analysis, "success": True}
    except Exception as e:
        return {"error": f"Failed to analyze page performance: {str(e)}"}


@tool("analyze_time_based_trends_tool")
def analyze_time_based_trends_tool() -> Dict[str, Any]:
    """
    Analyze time-based trends in crawl data.

    Returns:
        Dictionary containing the trend analysis.
    """
    try:
        trends = analyze_time_based_trends()
        return {"trends": trends, "success": True}
    except Exception as e:
        return {"error": f"Failed to analyze time-based trends: {str(e)}"}


@tool("get_recent_crawl_activity_tool")
def get_recent_crawl_activity_tool(limit: int = 10) -> Dict[str, Any]:
    """
    Get the most recent crawl activities.

    Args:
        limit: The number of recent activities to retrieve.

    Returns:
        Dictionary containing recent crawl activities.
    """
    try:
        activity = get_recent_crawl_activity(limit)
        return {"activity": activity, "success": True}
    except Exception as e:
        return {"error": f"Failed to get recent crawl activity: {str(e)}"}


@tool("cleanup_old_statistics_tool")
def cleanup_old_statistics_tool(days: int) -> Dict[str, Any]:
    """
    Cleanup old crawl statistics.

    Args:
        days: The age of records in days to cleanup.

    Returns:
        Dictionary confirming the cleanup operation.
    """
    try:
        cleanup_old_statistics(days)
        return {"message": f"Cleaned up statistics older than {days} days.", "success": True}
    except Exception as e:
        return {"error": f"Failed to cleanup old statistics: {str(e)}"}

import os
from typing import Dict, Any, Optional, Tuple, List, Union, Type
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from ddgs import DDGS

# Import existing functionality
from crawler import (
    crawl_website, crawl_local_file, save_links_to_file, _extract_page_title,
    crawl_multiple_websites, smart_crawler, SchemaLearner,
    download_html, download_multiple_html
)
from crawler.company_data import crawl_impressum, crawl_multiple_impressums
from utils.find_link_utils import find_impressum_link
from utils.file_utils import save_json_to_dir, save_summary_to_dir
from utils.preprocessing_utils import preprocess_impressum_content, extract_text_from_html
from agent.llm import get_summary_from_llm as get_summary, extract_company_data_from_impressum_llm as extract_company_data
from utils.logger import logger

# Import statistics tools
from crawler.statistics.tools import (
    get_crawl_statistics_summary,
    analyze_domain_performance,
    analyze_page_performance,
    analyze_time_based_trends,
    get_recent_crawl_activity,
    cleanup_old_statistics
)


# Input schemas for tools
class CrawlWebsiteInput(BaseModel):
    """Input schema for crawl_website_tool"""
    url: str = Field(description="The URL to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture a screenshot")
    pdf: bool = Field(default=False, description="Whether to generate a PDF")


class CrawlWithLinksInput(BaseModel):
    """Input schema for crawl_with_links_tool"""
    url: str = Field(description="URL to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture a screenshot")
    pdf: bool = Field(default=False, description="Whether to generate a PDF")
    extract_links: bool = Field(
        default=True, description="Whether to extract and categorize internal/external links")
    save_links: bool = Field(
        default=True, description="Whether to save extracted links to a JSON file")


class CrawlMultipleWebsitesInput(BaseModel):
    """Input schema for crawl_multiple_websites_tool"""
    urls: Union[str, List[str]] = Field(
        description="Single URL or list of URLs to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture screenshots")
    pdf: bool = Field(default=False, description="Whether to generate PDFs")
    extract_links: bool = Field(
        default=False, description="Whether to extract and categorize links")


class CrawlMultipleImpressumsInput(BaseModel):
    """Input schema for crawl_multiple_impressums_tool"""
    urls: Union[str, List[str]] = Field(
        description="Single URL or list of URLs to crawl impressums from")


class FindImpressumLinkInput(BaseModel):
    """Input schema for find_impressum_link_tool"""
    html_content: str = Field(
        description="HTML content to search for impressum links")
    base_url: str = Field(description="Base URL for resolving relative links")


class ExtractCompanyDataInput(BaseModel):
    """Input schema for extract_company_data_tool"""
    content: str = Field(description="Content to extract company data from")
    url: str = Field(description="URL of the source for context")


class GenerateSummaryInput(BaseModel):
    """Input schema for generate_summary_tool"""
    content: str = Field(description="Content to summarize")


class ProcessLocalFileInput(BaseModel):
    """Input schema for process_local_file_tool"""
    file_path: str = Field(description="Path to the local file to process")


class WebSearchInput(BaseModel):
    """Input schema for web_search_tool"""
    query: str = Field(description="Search query to search for")
    max_results: int = Field(
        default=10, description="Maximum number of search results to return")
    region: str = Field(
        default="us-en", description="Region for search results (e.g., 'us-en', 'de-de')")
    safesearch: str = Field(
        default="moderate", description="Safe search setting: 'on', 'moderate', 'off'")
    timelimit: Optional[str] = Field(
        default=None, description="Time limit for results: 'd' (day), 'w' (week), 'm' (month), 'y' (year)")


class SearchAndCrawlInput(BaseModel):
    """Input schema for search_and_crawl_tool"""
    query: str = Field(description="Search query to search for")
    max_results: int = Field(
        default=5, description="Maximum number of search results to crawl")
    screenshot: bool = Field(
        default=False, description="Whether to capture screenshots of crawled pages")
    pdf: bool = Field(
        default=False, description="Whether to generate PDFs of crawled pages")


class SmartCrawlerInput(BaseModel):
    """Input schema for smart_crawler_tool"""
    url: str = Field(description="The URL to crawl")
    target_model_schema: Dict[str, Any] = Field(
        description="JSON schema of the target Pydantic model")
    provider: str = Field(default="ollama/qwen3",
                          description="LLM provider for schema learning")
    api_token: Optional[str] = Field(
        default=None, description="API token for the LLM provider")
    force_learn: bool = Field(
        default=False, description="Force learning new schema even if cached one exists")
    schema_dir: str = Field(default="./learned_schemas",
                            description="Directory to store learned schemas")


class DownloadHtmlInput(BaseModel):
    """Input schema for download_html_tool"""
    url: str = Field(description="The URL to download HTML from")
    html_dir: str = Field(default="scraped_html",
                          description="Directory to save HTML files in")
    screenshot: bool = Field(
        default=False, description="Whether to capture a screenshot")
    pdf: bool = Field(default=False, description="Whether to generate a PDF")
    wait_until: str = Field(
        default="networkidle", description="When to consider page loading complete")
    page_timeout: int = Field(
        default=60000, description="Page load timeout in milliseconds")


class DownloadMultipleHtmlInput(BaseModel):
    """Input schema for download_multiple_html_tool"""
    urls: Union[str, List[str]] = Field(
        description="Single URL or list of URLs to download HTML from")
    html_dir: str = Field(default="scraped_html",
                          description="Directory to save HTML files in")
    screenshot: bool = Field(
        default=False, description="Whether to capture screenshots")
    pdf: bool = Field(default=False, description="Whether to generate PDFs")
    wait_until: str = Field(
        default="networkidle", description="When to consider page loading complete")
    page_timeout: int = Field(
        default=60000, description="Page load timeout in milliseconds")


# LangGraph Tools
@tool("crawl_website_tool", args_schema=CrawlWebsiteInput)
async def crawl_website_tool(url: str, screenshot: bool = False, pdf: bool = False) -> Dict[str, Any]:
    """
    Crawl a website and return its content along with file paths.

    Args:
        url: The URL to crawl
        screenshot: Whether to capture a screenshot
        pdf: Whether to generate a PDF

    Returns:
        Dictionary containing markdown content and file paths
    """
    try:
        logger.info(f"Crawling website: {url}")
        result = await crawl_website(url, screenshot, pdf, extract_links=False)
        markdown, html_path, screenshot_path, pdf_path, links_data, page_title = result

        if str(markdown).startswith("Error:"):
            return {"error": str(markdown)}

        return {
            "markdown_content": str(markdown),
            "saved_html_path": html_path,
            "screenshot_path": screenshot_path,
            "pdf_path": pdf_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error crawling website {url}: {e}")
        return {"error": f"Failed to crawl website: {str(e)}"}


@tool("crawl_with_links_tool", args_schema=CrawlWithLinksInput)
async def crawl_with_links_tool(
    url: str,
    screenshot: bool = False,
    pdf: bool = False,
    extract_links: bool = True,
    save_links: bool = True
) -> Dict[str, Any]:
    """
    Crawl a website and extract internal/external links along with content.

    Args:
        url (str): The URL to crawl
        screenshot (bool): Whether to capture a screenshot
        pdf (bool): Whether to generate a PDF
        extract_links (bool): Whether to extract and categorize links
        save_links (bool): Whether to save links to a JSON file

    Returns:
        Dict containing content, file paths, and link information
    """
    try:
        # Use the enhanced crawl_website function with link extraction
        result = await crawl_website(url, screenshot, pdf, extract_links)
        markdown_content, html_path, screenshot_path, pdf_path, links_data, page_title = result

        response = {
            "url": url,
            "page_title": page_title,
            "markdown_content": markdown_content,
            "html_path": html_path,
            "screenshot_path": screenshot_path,
            "pdf_path": pdf_path,
            "success": True
        }

        # Add link information if extracted
        if links_data:
            response["links"] = links_data
            response["link_summary"] = {
                "total_internal": len(links_data['internal']),
                "total_external": len(links_data['external']),
                "total_links": len(links_data['internal']) + len(links_data['external'])
            }

            # Save links to file if requested
            if save_links:
                links_file_path = save_links_to_file(
                    links_data, url, page_title)
                response["links_file_path"] = links_file_path

            logger.info(
                f"Extracted {response['link_summary']['total_links']} links from {url}")

        return response

    except Exception as e:
        logger.error(f"Error crawling website with links {url}: {e}")
        return {"error": f"Failed to crawl website with links: {str(e)}"}


@tool("crawl_multiple_websites_tool", args_schema=CrawlMultipleWebsitesInput)
async def crawl_multiple_websites_tool(
    urls: Union[str, List[str]],
    screenshot: bool = False,
    pdf: bool = False,
    extract_links: bool = False
) -> Dict[str, Any]:
    """
    Crawl multiple websites and return their content.

    Args:
        urls: Single URL string or list of URLs to crawl
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs
        extract_links: Whether to extract and categorize links

    Returns:
        Dict containing results for all crawled websites
    """
    try:
        logger.info(f"Crawling multiple websites: {urls}")
        results = await crawl_multiple_websites(urls, screenshot, pdf, extract_links)

        # Separate successful and failed results
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        response = {
            "total_urls": len(results),
            "successful_crawls": len(successful_results),
            "failed_crawls": len(failed_results),
            "results": results,
            "success": len(successful_results) > 0
        }

        # Add summary information
        if successful_results:
            response["successful_urls"] = [r["url"]
                                           for r in successful_results]

        if failed_results:
            response["failed_urls"] = [r["url"] for r in failed_results]
            response["errors"] = {r["url"]: r["error"] for r in failed_results}

        logger.info(
            f"Completed crawling {len(results)} URLs. Success: {len(successful_results)}/{len(results)}")
        return response

    except Exception as e:
        logger.error(f"Error crawling multiple websites: {e}")
        return {"error": f"Failed to crawl multiple websites: {str(e)}"}


@tool("crawl_multiple_impressums_tool", args_schema=CrawlMultipleImpressumsInput)
async def crawl_multiple_impressums_tool(urls: Union[str, List[str]]) -> Dict[str, Any]:
    """
    Crawl impressum pages from multiple websites and extract company data.

    Args:
        urls: Single URL string or list of URLs to crawl impressums from

    Returns:
        Dict containing impressum results with extracted company data for all websites
    """
    try:
        logger.info(f"Crawling multiple impressums: {urls}")
        results = await crawl_multiple_impressums(urls)

        # Process each result to extract company data
        processed_results = []
        for result in results:
            if result['success'] and result['markdown_content']:
                try:
                    # Extract company data from the markdown content
                    from agent.llm.client import extract_company_data_from_impressum_llm
                    company_data = await extract_company_data_from_impressum_llm(
                        content=result['markdown_content'],
                        url=result['url']
                    )

                    if "error" not in company_data:
                        # Save to JSON file
                        from utils.file_utils import save_json_to_dir
                        json_path = save_json_to_dir(company_data, result['url'])

                        # Add extracted data to result
                        result['company_data'] = company_data
                        result['json_path'] = json_path
                        logger.info(f"Successfully extracted company data from {result['url']}")
                    else:
                        result['extraction_error'] = company_data.get('error')
                        logger.warning(f"Failed to extract company data from {result['url']}: {company_data.get('error')}")

                except Exception as e:
                    result['extraction_error'] = f"Data extraction failed: {str(e)}"
                    logger.error(f"Error extracting company data from {result['url']}: {e}")

            processed_results.append(result)

        # Separate successful and failed results
        successful_results = [r for r in processed_results if r['success']]
        failed_results = [r for r in processed_results if not r['success']]

        response = {
            "total_urls": len(processed_results),
            "successful_crawls": len(successful_results),
            "failed_crawls": len(failed_results),
            "results": processed_results,
            "success": len(successful_results) > 0
        }

        # Add summary information
        if successful_results:
            response["successful_urls"] = [r["url"] for r in successful_results]
            # Count how many had successful data extraction
            extracted_count = sum(1 for r in successful_results if 'company_data' in r)
            response["successful_extractions"] = extracted_count

        if failed_results:
            response["failed_urls"] = [r["url"] for r in failed_results]
            response["errors"] = {r["url"]: r["error"] for r in failed_results}

        logger.info(
            f"Completed crawling impressums from {len(processed_results)} URLs. Success: {len(successful_results)}/{len(processed_results)}")
        return response

    except Exception as e:
        logger.error(f"Error crawling multiple impressums: {e}")
        return {"error": f"Failed to crawl multiple impressums: {str(e)}"}


@tool("crawl_impressum_tool")
async def crawl_impressum_tool(base_url: str) -> Dict[str, Any]:
    """
    Find and crawl the impressum/legal notice page of a website.

    Args:
        base_url: The base URL of the website

    Returns:
        Dictionary containing impressum content and file path
    """
    try:
        logger.info(f"Crawling impressum for: {base_url}")
        markdown, html_path = await crawl_impressum(base_url)

        if not markdown:
            return {"error": f"Could not find impressum page for {base_url}"}

        return {
            "markdown_content": markdown,
            "saved_html_path": html_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error crawling impressum for {base_url}: {e}")
        return {"error": f"Failed to crawl impressum: {str(e)}"}


@tool("find_impressum_link_tool", args_schema=FindImpressumLinkInput)
async def find_impressum_link_tool(html_content: str, base_url: str) -> Dict[str, Any]:
    """
    Find impressum/legal notice links in HTML content.

    Args:
        html_content: HTML content to search
        base_url: Base URL for resolving relative links

    Returns:
        Dictionary containing the impressum URL if found
    """
    try:
        impressum_url = await find_impressum_link(html_content, base_url)

        if impressum_url:
            return {"impressum_url": impressum_url, "success": True}
        else:
            return {"error": "No impressum link found"}
    except Exception as e:
        logger.error(f"Error finding impressum link: {e}")
        return {"error": f"Failed to find impressum link: {str(e)}"}


@tool("process_local_file_tool", args_schema=ProcessLocalFileInput)
async def process_local_file_tool(file_path: str) -> Dict[str, Any]:
    """
    Process a local HTML file and return its content.

    Args:
        file_path: Path to the local file

    Returns:
        Dictionary containing the processed content
    """
    try:
        logger.info(f"Processing local file: {file_path}")
        content = await crawl_local_file(file_path)

        if not content:
            return {"error": f"Could not process file: {file_path}"}

        return {
            "markdown_content": content,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error processing local file {file_path}: {e}")
        return {"error": f"Failed to process local file: {str(e)}"}


@tool("extract_company_data_tool", args_schema=ExtractCompanyDataInput)
async def extract_company_data_tool(content: str, url: str) -> Dict[str, Any]:
    """
    Extract structured company data from content using LLM.

    Args:
        content: Content to extract data from
        url: Source URL for context

    Returns:
        Dictionary containing extracted company data
    """
    try:
        logger.info(f"Extracting company data from content for: {url}")
        company_data = await extract_company_data(content, url)

        if "error" in company_data:
            return company_data

        json_file_path = save_json_to_dir(company_data, url)

        return {
            "company_data": company_data,
            "json_file_path": json_file_path,
            "success": True
        }
    except Exception as e:
        logger.error(f"Error extracting company data: {e}")
        return {"error": f"Failed to extract company data: {str(e)}"}


@tool("generate_summary_tool", args_schema=GenerateSummaryInput)
async def generate_summary_tool(content: str) -> Dict[str, Any]:
    """
    Generate a summary of content using LLM.

    Args:
        content: Content to summarize

    Search the web using DuckDuckGo and return search results.

    Args:
        query: Search query
        max_results: Maximum number of results to return
        region: Region for search results
        safesearch: Safe search setting
        timelimit: Time limit for results

    Returns:
        Dictionary containing search results
    """
    try:
        logger.info(f"Searching web for: {query}")

        with DDGS() as ddgs:
            results = list(ddgs.text(
                keywords=query,
                region=region,
                safesearch=safesearch,
                timelimit=timelimit,
                max_results=max_results
            ))

        if not results:
            return {"error": f"No search results found for query: {query}"}

        # Format results for better readability
        formatted_results = []
        for i, result in enumerate(results, 1):
            formatted_result = {
                "rank": i,
                "title": result.get("title", ""),
                "url": result.get("href", ""),
                "snippet": result.get("body", ""),
                "source": result.get("source", "")
            }
            formatted_results.append(formatted_result)

        logger.info(f"Found {len(formatted_results)} search results")

        return {
            "query": query,
            "results": formatted_results,
            "total_results": len(formatted_results),
            "success": True
        }

    except Exception as e:
        logger.error(f"Error performing web search: {e}")
        return {"error": f"Failed to perform web search: {str(e)}"}


@tool("search_and_crawl_tool", args_schema=SearchAndCrawlInput)
async def search_and_crawl_tool(
    query: str,
    max_results: int = 5,
    screenshot: bool = False,
    pdf: bool = False
) -> Dict[str, Any]:
    """
    Search the web and crawl the top results.

    Args:
        query: Search query
        max_results: Maximum number of results to crawl
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs

    Returns:
        Dictionary containing search results and crawled content
    """
    try:
        logger.info(f"Searching and crawling for: {query}")

        # First, perform the search
        search_result = web_search_tool.invoke({
            "query": query,
            "max_results": max_results
        })

        if "error" in search_result:
            return search_result

        # Then crawl each result
        crawled_results = []
        search_results = search_result.get("search_results", [])

        for i, result in enumerate(search_results[:max_results], 1):
            url = result["href"]
            logger.info(f"Crawling result {i}/{max_results}: {url}")

            try:
                crawl_result = await crawl_website_tool.ainvoke({
                    "url": url,
                    "screenshot": screenshot,
                    "pdf": pdf
                })

                if "error" not in crawl_result:
                    crawled_result = {
                        "rank": i,
                        "title": result["title"],
                        "url": url,
                        "snippet": result["body"],
                        "markdown_content": crawl_result["markdown_content"],
                        "saved_html_path": crawl_result.get("saved_html_path"),
                        "screenshot_path": crawl_result.get("screenshot_path"),
                        "pdf_path": crawl_result.get("pdf_path")
                    }
                else:
                    crawled_result = {
                        "rank": i,
                        "title": result["title"],
                        "url": url,
                        "snippet": result["body"],
                        "error": crawl_result["error"]
                    }

                crawled_results.append(crawled_result)

            except Exception as e:
                logger.error(f"Error crawling {url}: {e}")
                crawled_results.append({
                    "rank": i,
                    "title": result["title"],
                    "url": url,
                    "body": result["body"],
                    "error": f"Failed to crawl: {str(e)}"
                })

        successful_crawls = len(
            [r for r in crawled_results if "error" not in r])

        return {
            "query": query,
            "search_results": search_results,
            "crawled_results": crawled_results,
            "total_searched": len(search_results),
            "total_crawled": len(crawled_results),
            "successful_crawls": successful_crawls,
            "success": True
        }

    except Exception as e:
        logger.error(f"Error in search and crawl: {e}")
        return {"error": f"Failed to search and crawl: {str(e)}"}


@tool("smart_crawler_tool", args_schema=SmartCrawlerInput)
async def smart_crawler_tool(
    url: str,
    target_model_schema: Dict[str, Any],
    provider: str = "ollama/qwen3",
    api_token: Optional[str] = None,
    force_learn: bool = False,
    schema_dir: str = "./learned_schemas"
) -> Dict[str, Any]:
    """
    Crawl a webpage using schema learning for structured data extraction.

    Args:
        url: The URL to crawl
        target_model_schema: JSON schema of the target Pydantic model
        provider: LLM provider for schema learning
        api_token: API token for the LLM provider
        force_learn: Force learning new schema even if cached one exists
        schema_dir: Directory to store learned schemas

    Returns:
        Dictionary containing extracted structured data
    """
    try:
        logger.info(f"Smart crawling URL: {url}")

        # Create a temporary Pydantic model from the schema
        # This is a simplified approach - in practice you might want to
        # create the model more dynamically
        class TempModel(BaseModel):
            pass

        # Set the schema on the temporary model
        TempModel.model_json_schema = lambda: target_model_schema

        # Use the smart_crawler function
        extracted_data = await smart_crawler(
            url=url,
            target_model=TempModel,
            provider=provider,
            api_token=api_token,
            force_learn=force_learn,
            schema_dir=schema_dir
        )

        return {
            "url": url,
            "extracted_data": extracted_data,
            "schema_used": target_model_schema,
            "provider": provider,
            "success": True
        }

    except Exception as e:
        logger.error(f"Error in smart crawler for {url}: {e}")
        return {"error": f"Failed to smart crawl: {str(e)}"}


@tool("download_html_tool", args_schema=DownloadHtmlInput)
async def download_html_tool(
    url: str,
    html_dir: str = "scraped_html",
    screenshot: bool = False,
    pdf: bool = False,
    wait_until: str = "networkidle",
    page_timeout: int = 60000
) -> Dict[str, Any]:
    """
    Download and save HTML content from a website with minimal processing.
    This tool focuses specifically on HTML downloading without markdown conversion.
    Optionally capture screenshots and PDFs.

    Args:
        url: The URL to download HTML from
        html_dir: Directory to save HTML files in
        screenshot: Whether to capture a screenshot
        pdf: Whether to generate a PDF
        wait_until: When to consider page loading complete
        page_timeout: Page load timeout in milliseconds

    Returns:
        Dictionary containing HTML file path, page title, and optional media paths
    """
    try:
        logger.info(f"Downloading HTML from: {url}")
        html_path, page_title, screenshot_path, pdf_path = await download_html(
            url, html_dir, screenshot, pdf, wait_until, page_timeout
        )

        if html_path:
            return {
                "url": url,
                "html_path": html_path,
                "page_title": page_title,
                "screenshot_path": screenshot_path,
                "pdf_path": pdf_path,
                "html_dir": html_dir,
                "success": True
            }
        else:
            return {"error": f"Failed to download HTML from {url}"}

    except Exception as e:
        logger.error(f"Error downloading HTML from {url}: {e}")
        return {"error": f"Failed to download HTML: {str(e)}"}


@tool("download_multiple_html_tool", args_schema=DownloadMultipleHtmlInput)
async def download_multiple_html_tool(
    urls: Union[str, List[str]],
    html_dir: str = "scraped_html",
    screenshot: bool = False,
    pdf: bool = False,
    wait_until: str = "networkidle",
    page_timeout: int = 60000
) -> Dict[str, Any]:
    """
    Download and save HTML content from multiple websites.
    This tool focuses specifically on HTML downloading without markdown conversion.
    Optionally capture screenshots and PDFs.

    Args:
        urls: Single URL string or list of URLs to download HTML from
        html_dir: Directory to save HTML files in
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs
        wait_until: When to consider page loading complete
        page_timeout: Page load timeout in milliseconds

    Returns:
        Dictionary containing download results for all URLs
    """
    try:
        logger.info(f"Downloading HTML from multiple URLs: {urls}")
        results = await download_multiple_html(urls, html_dir, screenshot, pdf, wait_until, page_timeout)

        # Separate successful and failed results
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        response = {
            "total_urls": len(results),
            "successful_downloads": len(successful_results),
            "failed_downloads": len(failed_results),
            "results": results,
            "html_dir": html_dir,
            "success": len(successful_results) > 0
        }

        # Add summary information
        if successful_results:
            response["successful_urls"] = [r["url"]
                                           for r in successful_results]
            response["html_files"] = [r["html_path"]
                                      for r in successful_results if r["html_path"]]

        if failed_results:
            response["failed_urls"] = [r["url"] for r in failed_results]
            response["errors"] = {r["url"]: r["error"] for r in failed_results}

        logger.info(
            f"Completed HTML download for {len(results)} URLs. Success: {len(successful_results)}/{len(results)}")
        return response

    except Exception as e:
        logger.error(f"Error downloading HTML from multiple URLs: {e}")
        return {"error": f"Failed to download HTML from multiple URLs: {str(e)}"}


# List of all tools for easy import
CRAWL_TOOLS = [
    crawl_website_tool,
    crawl_impressum_tool,
    find_impressum_link_tool,
    process_local_file_tool,
    extract_company_data_tool,
    generate_summary_tool,
    save_json_data_tool,
    save_summary_tool,
    web_search_tool,
    search_and_crawl_tool,
    smart_crawler_tool,
    crawl_with_links_tool,
    crawl_multiple_websites_tool,
    crawl_multiple_impressums_tool,
    download_html_tool,
    download_multiple_html_tool,
]
