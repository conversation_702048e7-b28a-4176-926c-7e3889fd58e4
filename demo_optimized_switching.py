#!/usr/bin/env python3
"""
Demo of the optimized multi-provider switching system.
Shows how easy it is to switch between providers and test them.
"""

import asyncio
import os
import subprocess
from agent.llm.client import get_chat_response_from_llm


def set_env_vars():
    """Set environment variables for testing."""
    os.environ['OPENAI_API_KEY'] = '********************************************************************************************************************************************************************'
    os.environ['GROQ_API_KEY'] = '********************************************************'


def switch_provider(profile_name):
    """Switch provider using the command-line tool."""
    result = subprocess.run(['python', 'switch_provider.py', profile_name], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print(f"✅ Successfully switched to {profile_name}")
        return True
    else:
        print(f"❌ Failed to switch to {profile_name}: {result.stderr}")
        return False


async def test_current_provider(provider_name):
    """Test the currently active provider."""
    print(f"\n🧪 Testing {provider_name}...")
    try:
        response = await get_chat_response_from_llm(
            f"Hello! Please respond with: '{provider_name} is working perfectly!'"
        )
        print(f"   Response: {response}")
        return True
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return False


async def demo_optimized_switching():
    """Demonstrate the optimized provider switching system."""
    print("🚀 Optimized Multi-Provider Switching Demo")
    print("=" * 60)
    
    # Set environment variables
    set_env_vars()
    
    # Test different providers
    providers_to_test = [
        ("ollama_local", "Local Ollama"),
        ("openai_fast", "OpenAI GPT-4o-mini"),
        ("groq_speed", "Groq Llama 3.1")
    ]
    
    results = {}
    
    for profile, description in providers_to_test:
        print(f"\n{'='*20} {description} {'='*20}")
        
        # Switch provider
        if switch_provider(profile):
            # Test the provider
            success = await test_current_provider(profile)
            results[profile] = success
        else:
            results[profile] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 OPTIMIZED SWITCHING DEMO RESULTS")
    print("=" * 60)
    
    for profile, success in results.items():
        status = "✅ WORKING" if success else "❌ FAILED"
        print(f"{profile:15} | {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 Result: {working_count}/{total_count} providers working")
    
    if working_count > 0:
        print("\n💡 Key Benefits of Optimized System:")
        print("   ✅ One-command provider switching")
        print("   ✅ API keys secured in environment variables")
        print("   ✅ No API keys in git repository")
        print("   ✅ Easy profile management")
        print("   ✅ Backward compatibility maintained")
        
        print("\n🔄 Easy Switching Commands:")
        print("   python switch_provider.py                # List all providers")
        print("   python switch_provider.py ollama_local   # Switch to Ollama")
        print("   python switch_provider.py openai_fast    # Switch to OpenAI")
        print("   python switch_provider.py groq_speed     # Switch to Groq")


def show_before_after():
    """Show the improvement from old to new system."""
    print("\n" + "=" * 60)
    print("📈 BEFORE vs AFTER OPTIMIZATION")
    print("=" * 60)
    
    print("\n❌ BEFORE (Cumbersome):")
    print("   • API keys hardcoded in config.ini")
    print("   • Manual editing of provider and model fields")
    print("   • Risk of committing secrets to git")
    print("   • No easy way to switch between configurations")
    print("   • Complex setup for different use cases")
    
    print("\n✅ AFTER (Optimized):")
    print("   • API keys in environment variables (.env file)")
    print("   • One-command provider switching")
    print("   • Git-safe configuration (no secrets)")
    print("   • Pre-configured profiles for common use cases")
    print("   • Simple setup with setup_env.py")
    
    print("\n🎯 Switching Comparison:")
    print("   Before: Edit config.ini → Change provider → Change model → Restart")
    print("   After:  python switch_provider.py openai_fast")


if __name__ == "__main__":
    print("🎭 Optimized Multi-Provider System Demo")
    print("This demonstrates the improved provider switching system")
    
    # Show improvements
    show_before_after()
    
    # Run the demo
    asyncio.run(demo_optimized_switching())
    
    print("\n" + "=" * 60)
    print("🏁 Demo completed!")
    print("Your system is now optimized for easy provider switching!")
    print("=" * 60)
