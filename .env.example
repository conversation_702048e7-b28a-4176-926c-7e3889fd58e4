# Environment Variables for LiteLLM Multi-Provider Setup
# =====================================================
# Copy this file to .env and fill in your actual API keys
# The .env file should NOT be committed to git (it's in .gitignore)

# OpenAI API Key
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Groq API Key  
# Get from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# Optional: Custom Ollama URL (if not using default localhost:11434)
# OLLAMA_BASE_URL=http://localhost:11434

# Optional: Custom model names (override config.ini defaults)
# OLLAMA_MODEL=qwen3
# OPENAI_MODEL=gpt-4o-mini
# GROQ_MODEL=llama-3.1-8b-instant
