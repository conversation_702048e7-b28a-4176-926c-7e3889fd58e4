[litellm]
active_provider = ollama_local

[profile_ollama_local]
provider = ollama
model = qwen3
description = Local Ollama with Qwen3 (free, private)

[profile_openai_fast]
provider = openai
model = gpt-4o-mini
description = OpenAI GPT-4o-mini (fast, cost-effective)

[profile_openai_quality]
provider = openai
model = gpt-4o
description = OpenAI GPT-4o (highest quality)

[profile_groq_speed]
provider = groq
model = llama-3.1-8b-instant
description = Groq Llama 3.1 (fastest inference)

[profile_groq_large]
provider = groq
model = llama-3.1-70b-versatile
description = Groq Llama 3.1 70B (large model)

[ollama]
base_url = http://localhost:11434

[openai]
api_key = ${OPENAI_API_KEY}

[groq]
api_key = ${GROQ_API_KEY}

[logging]
level = INFO

