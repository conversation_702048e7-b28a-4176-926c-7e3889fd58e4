# LiteLLM Configuration
[litellm]
# The default provider to use. Options: "ollama", "openai", "groq"
provider = ollama

# The primary model to use for all tasks.
# For Ollama, this is the model name (e.g., "qwen3", "llama3").
# For OpenAI, this is the model name (e.g., "gpt-4o", "gpt-3.5-turbo").
# For Groq, this is the model name (e.g., "llama3-8b-8192", "mixtral-8x7b-32768").
primary_model = qwen3

# A comma-separated list of fallback models to try if the primary model fails.
fallback_models =

# --- Provider-Specific Settings ---

[ollama]
# The base URL for the Ollama API.
# This is only needed if your Ollama instance is not at the default localhost address.
base_url = http://localhost:11434

[openai]
# Your OpenAI API key. It is recommended to set this as an environment variable (OPENAI_API_KEY).
api_key = your_openai_api_key_here

[groq]
# Your GroqCloud API key. It is recommended to set this as an environment variable (GROQ_API_KEY).
api_key = your_groq_api_key_here

[logging]
# Log level for the application. Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO