# Crawl4AI Agent 🚀

A powerful agentic web crawler built with LangGraph that understands natural language commands and orchestrates complex crawling workflows with multi-provider LLM support.

## ✨ Features

### 🧠 **Multi-Provider LLM Architecture**
- **Unified Interface**: Support for Ollama, OpenAI, and Groq through LiteLLM
- **Easy Provider Switching**: One-command switching between providers
- **Secure Configuration**: Environment variable-based API key management
- **Service-Oriented**: Modular LLM services for different operations

### 🌐 **Advanced Web Crawling**
- **Natural Language Interface**: Describe what you want in plain English
- **Smart Content Extraction**: Automatic content processing and structuring
- **Multi-Format Output**: HTML, Markdown, JSON, PDF, and screenshots
- **Batch Processing**: Handle multiple URLs simultaneously
- **Link Analysis**: Extract and categorize internal/external links

### 🏢 **Business Data Extraction**
- **Impressum Crawler**: Specialized extraction of company legal information
- **Structured Output**: JSON format with consistent schema
- **Multi-Language Support**: Works with various European legal notice formats
- **Batch Company Processing**: Extract data from multiple companies

### 📊 **Analytics & Statistics**
- **Crawl Tracking**: Monitor domains, pages, and visit frequency
- **Performance Analysis**: Domain and page-level performance metrics
- **Historical Data**: Import and analyze past crawling activities
- **Reporting Tools**: Generate comprehensive crawl reports

### 🔧 **Developer Experience**
- **LangGraph Architecture**: Advanced agentic workflow orchestration
- **Comprehensive Testing**: 18+ test suites ensuring reliability
- **Extensive Documentation**: Complete setup and usage guides
- **Error Handling**: Robust error management and recovery

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js (for Playwright)
- Ollama (for local LLM) or API keys for OpenAI/Groq

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd Demo-Crawl4AI
```

2. **Set up virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Install Playwright browsers**
```bash
playwright install chromium
```

5. **Set up environment variables**
```bash
python setup_env.py
```

Edit the created `.env` file with your API keys:
```bash
OPENAI_API_KEY=your_openai_key_here
GROQ_API_KEY=your_groq_key_here
```

### Configuration

The application uses a profile-based configuration system in `config.ini`:

```ini
[litellm]
# Active provider profile
active_provider = ollama_local

# Pre-configured profiles
[profile_ollama_local]
provider = ollama
model = qwen3
description = Local Ollama with Qwen3 (free, private)

[profile_openai_fast]
provider = openai
model = gpt-4o-mini
description = OpenAI GPT-4o-mini (fast, cost-effective)

[profile_groq_speed]
provider = groq
model = llama-3.1-8b-instant
description = Groq Llama 3.1 (fastest inference)
```

## 🎯 Usage

### Starting the Application

```bash
# Interactive interface selection
python main.py

# Direct LangGraph interface (recommended)
python main.py --langgraph

# Traditional chat interface
python main.py --traditional
```

### Provider Switching

```bash
# List available providers
python switch_provider.py

# Switch to specific provider
python switch_provider.py openai_fast    # OpenAI GPT-4o-mini
python switch_provider.py groq_speed     # Groq Llama 3.1
python switch_provider.py ollama_local   # Local Ollama
```

### Natural Language Commands

The LangGraph interface understands natural language. Try these examples:

```
# Basic crawling
"Crawl https://example.com"
"Get the content from https://news.site.com with screenshot"

# Company data extraction
"Extract impressum data from https://company.com"
"Get company information from multiple sites: site1.com, site2.com"

# Content analysis
"Summarize the content from https://blog.com"
"Crawl https://docs.site.com and save as PDF"

# Batch operations
"Crawl these URLs and generate summaries: url1.com, url2.com, url3.com"

# Analytics
"Show crawl statistics"
"Analyze domain performance for example.com"
"Show recent crawling activity"
```

## 🔧 Advanced Features

### Multi-Provider LLM Usage

Choose the right provider for your use case:

- **Ollama (Local)**: Free, private, no API costs
- **OpenAI**: High quality, reliable, good for production
- **Groq**: Very fast inference, good for real-time applications

### Batch Processing

```python
# Process multiple URLs
urls = ["https://site1.com", "https://site2.com", "https://site3.com"]
"Crawl and summarize these URLs: " + ", ".join(urls)
```

### Company Data Extraction

```python
# Extract structured company data
"Extract impressum from https://company.de"

# Batch company processing
"Get company data from: company1.de, company2.com, company3.org"
```

### Analytics and Reporting

```python
# Get crawl statistics
"Show crawl statistics summary"

# Domain analysis
"Analyze performance for domain.com"

# Recent activity
"Show last 50 crawl activities"
```

## 📁 Project Structure

```
Demo-Crawl4AI/
├── agent/                    # LangGraph agent implementation
│   ├── llm/                 # Multi-provider LLM services
│   ├── langgraph_*.py       # Workflow nodes and tools
│   └── chat_loop.py         # Traditional interface
├── crawler/                 # Core crawling functionality
│   ├── core.py             # Basic crawling operations
│   ├── company_data.py     # Impressum extraction
│   ├── statistics/         # Analytics and tracking
│   └── extractors/         # Data extraction services
├── config/                  # Configuration management
├── utils/                   # Utility functions
├── tests/                   # Comprehensive test suite
└── logs/                    # Application logs

## 🧪 Testing

The project includes comprehensive testing infrastructure with 18+ test suites:

### Running Tests

```bash
# Run all tests
python -m pytest

# Run specific test categories
python -m pytest tests/agent/test_llm_client_integration.py -v
python -m pytest tests/agent/test_end_to_end_workflow.py -v

# Run with coverage
python -m pytest --cov=agent --cov=crawler
```

### Test Categories

- **LiteLLM Integration Tests**: Multi-provider LLM functionality
- **Migration Compatibility Tests**: Backward compatibility verification
- **End-to-End Workflow Tests**: Complete user request processing
- **Error Handling Tests**: Network failures and edge cases
- **Playwright Verification**: Browser functionality and stability

### Test Status: ✅ 18/18 Passing

All tests are currently passing, ensuring system reliability and stability.

## 🛠️ Configuration

### Environment Variables

Create a `.env` file with your API keys:

```bash
# OpenAI API Key (get from: https://platform.openai.com/api-keys)
OPENAI_API_KEY=your_openai_api_key_here

# Groq API Key (get from: https://console.groq.com/keys)
GROQ_API_KEY=your_groq_api_key_here
```

### Provider Profiles

The `config.ini` file contains pre-configured profiles for different use cases:

| Profile | Provider | Model | Use Case |
|---------|----------|-------|----------|
| `ollama_local` | Ollama | qwen3 | Free, private, local |
| `openai_fast` | OpenAI | gpt-4o-mini | Fast, cost-effective |
| `openai_quality` | OpenAI | gpt-4o | Highest quality |
| `groq_speed` | Groq | llama-3.1-8b-instant | Fastest inference |
| `groq_large` | Groq | llama-3.1-70b-versatile | Large model |

### Logging Configuration

Logging is configured in `config.ini`:

```ini
[logging]
level = INFO  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
```

Logs are written to:
- **Console**: Real-time output during execution
- **Files**: Timestamped log files in `/logs` directory

## 🔍 Troubleshooting

### Common Issues

#### 1. **Playwright Browser Issues**
```bash
# Reinstall Playwright browsers
playwright install chromium

# Check browser installation
playwright install --help
```

#### 2. **Ollama Connection Issues**
```bash
# Check Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama service
ollama serve
```

#### 3. **API Key Issues**
```bash
# Verify environment variables
python -c "import os; print('OpenAI:', bool(os.getenv('OPENAI_API_KEY'))); print('Groq:', bool(os.getenv('GROQ_API_KEY')))"

# Test provider setup
python setup_env.py
```

#### 4. **Network Issues**
- Check internet connection and firewall settings
- Verify target websites are accessible
- Consider using VPN if geo-blocked

### Debug Mode

Enable debug logging for detailed troubleshooting:

```ini
[logging]
level = DEBUG
```

## 📚 Documentation

### Additional Resources

- **CHANGELOG.md**: Detailed version history and changes
- **PROVIDER_OPTIMIZATION.md**: Multi-provider setup guide
- **PLAYWRIGHT_FIX.md**: Browser troubleshooting guide
- **MIGRATION_SUMMARY.md**: Architecture migration details

### API Reference

The application provides both programmatic and natural language interfaces:

#### Natural Language Interface (Recommended)
```python
from agent.langgraph_workflow import CrawlAgent

agent = CrawlAgent()
result = await agent.process_request("Crawl https://example.com")
```

#### Direct API Usage
```python
from crawler.core import crawl_website
from agent.llm import get_summary_from_llm

# Crawl website
result = await crawl_website("https://example.com")

# Generate summary
summary = await get_summary_from_llm(result['markdown_content'])
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests (`python -m pytest`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests with coverage
python -m pytest --cov=agent --cov=crawler --cov-report=html

# Run linting
flake8 agent/ crawler/ utils/
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Crawl4AI**: Core crawling engine
- **LangGraph**: Agentic workflow orchestration
- **LiteLLM**: Multi-provider LLM interface
- **Playwright**: Browser automation
- **Ollama**: Local LLM inference

---

**Ready to crawl the web intelligently?** 🕷️✨

Start with `python main.py --langgraph` and describe what you want to crawl in natural language!
```
