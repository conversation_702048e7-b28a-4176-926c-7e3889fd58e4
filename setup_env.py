#!/usr/bin/env python3
"""
Environment setup utility for LiteLLM multi-provider configuration.
Helps set up API keys securely without committing them to git.
"""

import os
import sys
from pathlib import Path


def create_env_file():
    """Create .env file from template if it doesn't exist."""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    # Copy example to .env
    with open(env_example, 'r') as f:
        content = f.read()
    
    with open(env_file, 'w') as f:
        f.write(content)
    
    print("✅ Created .env file from template")
    print("📝 Please edit .env file and add your actual API keys")
    return True


def check_env_vars():
    """Check which environment variables are set."""
    required_vars = {
        'OPENAI_API_KEY': 'OpenAI API access',
        'GROQ_API_KEY': 'Groq API access'
    }
    
    print("🔍 Environment Variables Status:")
    print("=" * 40)
    
    all_set = True
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            # Show only first/last few characters for security
            masked = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else "***"
            print(f"✅ {var}: {masked}")
        else:
            print(f"❌ {var}: Not set ({description})")
            all_set = False
    
    return all_set


def setup_instructions():
    """Show setup instructions."""
    print("\n📋 Setup Instructions:")
    print("=" * 40)
    print("1. Get your API keys:")
    print("   • OpenAI: https://platform.openai.com/api-keys")
    print("   • Groq: https://console.groq.com/keys")
    print()
    print("2. Set environment variables:")
    print("   Option A - Using .env file (recommended):")
    print("     • Edit .env file and add your keys")
    print("     • The app will load them automatically")
    print()
    print("   Option B - Export in terminal:")
    print("     export OPENAI_API_KEY='your-openai-key'")
    print("     export GROQ_API_KEY='your-groq-key'")
    print()
    print("3. Test your setup:")
    print("   python test_providers.py")
    print()
    print("4. Switch providers easily:")
    print("   python switch_provider.py                # List providers")
    print("   python switch_provider.py openai_fast    # Switch to OpenAI")
    print("   python switch_provider.py groq_speed     # Switch to Groq")


def load_env_file():
    """Load environment variables from .env file."""
    env_file = Path('.env')
    if not env_file.exists():
        return False
    
    loaded_count = 0
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                
                if value and not value.startswith('your_'):
                    os.environ[key] = value
                    loaded_count += 1
    
    if loaded_count > 0:
        print(f"✅ Loaded {loaded_count} environment variables from .env file")
    
    return loaded_count > 0


def main():
    """Main setup function."""
    print("🔧 LiteLLM Environment Setup")
    print("=" * 40)
    
    # Create .env file if needed
    create_env_file()
    
    # Load .env file
    load_env_file()
    
    # Check current status
    all_set = check_env_vars()
    
    if all_set:
        print("\n🎉 All environment variables are set!")
        print("✅ You're ready to use all LLM providers")
        
        # Test if we can import and use the config
        try:
            from agent.llm.enhanced_config import list_available_profiles
            print("\n📋 Available provider profiles:")
            list_available_profiles()
        except Exception as e:
            print(f"\n⚠️  Warning: Could not load provider profiles: {e}")
    else:
        print("\n⚠️  Some environment variables are missing")
        setup_instructions()


if __name__ == "__main__":
    main()
